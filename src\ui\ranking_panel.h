#ifndef RANKING_PANEL_H
#define RANKING_PANEL_H

#include <QWidget>
#include <QTableWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QTimer>
#include <QHeaderView>
#include "core/rules_engine.h"

/**
 * @brief 实时排名面板组件
 * 
 * 这个组件负责显示跳高比赛的实时排名信息，包括：
 * - 运动员排名列表
 * - 最佳成绩显示
 * - 实时更新功能
 * - 排名变化动画
 */
class RankingPanel : public QWidget
{
    Q_OBJECT

public:
    explicit RankingPanel(QWidget *parent = nullptr);
    ~RankingPanel();

    /**
     * @brief 设置比赛ID
     * @param competitionId 比赛ID
     */
    void setCompetitionId(int competitionId);

    /**
     * @brief 刷新排名显示
     */
    void refreshRanking();

    /**
     * @brief 设置自动刷新间隔
     * @param intervalMs 刷新间隔（毫秒）
     */
    void setAutoRefreshInterval(int intervalMs);

    /**
     * @brief 启用/禁用自动刷新
     * @param enabled 是否启用
     */
    void setAutoRefreshEnabled(bool enabled);

public slots:
    /**
     * @brief 处理排名更新
     * @param rankings 新的排名列表
     */
    void onRankingUpdated(const QList<RulesEngine::RankingEntry> &rankings);

    /**
     * @brief 处理排名错误
     * @param error 错误信息
     */
    void onRankingError(const QString &error);

private slots:
    /**
     * @brief 自动刷新定时器
     */
    void onAutoRefreshTimer();

    /**
     * @brief 手动刷新按钮点击
     */
    void onRefreshButtonClicked();

private:
    // UI初始化
    void setupUI();
    void setupTable();
    void setupControls();
    void setupConnections();

    // 数据更新
    void updateRankingTable(const QList<RulesEngine::RankingEntry> &rankings);
    void clearRankingTable();
    void showErrorMessage(const QString &error);

    // 样式设置
    void applyTableStyles();
    void highlightRankingChanges();

    // UI组件
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_controlLayout;
    
    QLabel *m_titleLabel;
    QLabel *m_statusLabel;
    QLabel *m_lastUpdateLabel;
    QPushButton *m_refreshButton;
    
    QTableWidget *m_rankingTable;
    
    // 数据
    int m_competitionId;
    QList<RulesEngine::RankingEntry> m_currentRankings;
    QList<RulesEngine::RankingEntry> m_previousRankings;
    
    // 自动刷新
    QTimer *m_autoRefreshTimer;
    bool m_autoRefreshEnabled;
    int m_autoRefreshInterval;
    
    // 表格列索引
    enum ColumnIndex {
        RankColumn = 0,
        NameColumn = 1,
        BestHeightColumn = 2,
        FailuresAtBestColumn = 3,
        TotalFailuresColumn = 4
    };
    
    // 常量
    static const int DEFAULT_AUTO_REFRESH_INTERVAL = 5000; // 5 seconds
    static const int MIN_AUTO_REFRESH_INTERVAL = 1000;     // 1 second
    static const int MAX_AUTO_REFRESH_INTERVAL = 60000;    // 60 seconds
};

#endif // RANKING_PANEL_H
