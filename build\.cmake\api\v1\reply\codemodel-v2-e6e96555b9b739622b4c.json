{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-Debug-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-79965f33bd9d76a71fac.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-Debug-4fedd1e22d46271b8a78.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-Debug-bc1ee51b380a7cf3c713.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-Debug-e40858e7cea6ac21db66.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-Debug-81f3a81c0016cb373ec5.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-Debug-7a4a305bc801bf55f331.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-Debug-2b321c69170339dbdd30.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-Debug-eb5a12cc2e79c2ac8dc9.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-Debug-1445f807db117c14730c.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-Debug-17a3f780a1d13aaf4f88.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-Debug-0c1e4798e871f5013276.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_rules_engine::@a44f0ac069e85531cdee", "jsonFile": "target-test_rules_engine-Debug-e1712963593a5a9fdad7.json", "name": "test_rules_engine", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-Debug-168b73c6c6aebd8cd0bc.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-Debug-25a35cfd5bcaac757667.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-Debug-853d0c05c0011173f9f3.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-Release-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "name": "Release", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-79965f33bd9d76a71fac.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-Release-ce0f188764d8666823b3.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-Release-76b56ce9ae9315129c75.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-Release-b47a6b1f1d54da56b372.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-Release-0050b628fe9797077d84.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-Release-938bb69452038bccaf68.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-Release-ffb463992f78bc7ad70e.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-Release-c495c755dad9c66be354.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-Release-bb8be21257a361ee87d5.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-Release-3a604ab24dd950239d8b.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-Release-36ea5f1676742ee7013c.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_rules_engine::@a44f0ac069e85531cdee", "jsonFile": "target-test_rules_engine-Release-e6bfb76b80ee234b04e9.json", "name": "test_rules_engine", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-Release-b1127b37a6264b7fe94a.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-Release-57b5d285540676364622.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-Release-23a75b91c621d4ad2aab.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-MinSizeRel-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-79965f33bd9d76a71fac.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-MinSizeRel-1a74be67d70881c27f36.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-MinSizeRel-c7d67c54f1045ca37082.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-MinSizeRel-c15cad11835aab6f1259.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-MinSizeRel-f4265b3b4c0ab0f77cda.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-MinSizeRel-9e86ad5fd688e015924f.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-MinSizeRel-bcae970bd5cb165258da.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-MinSizeRel-6c67dcc3d4426901cb9e.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-MinSizeRel-4e856f33469dfb9566cc.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-MinSizeRel-200105669a1a958298f8.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-MinSizeRel-0c0b99db898a2e66827f.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_rules_engine::@a44f0ac069e85531cdee", "jsonFile": "target-test_rules_engine-MinSizeRel-e2e4d34f21efdaf1f7bf.json", "name": "test_rules_engine", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-MinSizeRel-bf309a12e76536e234f9.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-MinSizeRel-6dfd81a5a3d55cf7758c.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-MinSizeRel-85e3f068e96604e315b1.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-RelWithDebInfo-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-79965f33bd9d76a71fac.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-RelWithDebInfo-76f7a85953030cf9a4d8.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-RelWithDebInfo-1b1fa5c91d947112cdcd.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-RelWithDebInfo-45fa27caeffe1d73baf0.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-RelWithDebInfo-fc11a29c892f69169b50.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-RelWithDebInfo-0c53b2eb7c465711f92a.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-RelWithDebInfo-ee41b36e3a38f417021c.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-RelWithDebInfo-98eae4c203eea0d4ae81.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-RelWithDebInfo-507e5ea46a13f93d71b8.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-RelWithDebInfo-6ff7495e1e3d599e1f40.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-RelWithDebInfo-68793fed496974d24b0f.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_rules_engine::@a44f0ac069e85531cdee", "jsonFile": "target-test_rules_engine-RelWithDebInfo-665437a5591145101857.json", "name": "test_rules_engine", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-RelWithDebInfo-6963b460fd378fe3e5cc.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-RelWithDebInfo-3d92d005ca1731a2439d.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-RelWithDebInfo-86f9dbe78cf8cf9a7c5b.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/PROJECT/HighJump/build", "source": "C:/PROJECT/HighJump"}, "version": {"major": 2, "minor": 8}}