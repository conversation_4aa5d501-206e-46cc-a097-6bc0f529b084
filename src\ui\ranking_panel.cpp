#include "ranking_panel.h"
#include "core/rules_engine.h"
#include <QDateTime>
#include <QDebug>
#include <QHeaderView>
#include <QTableWidgetItem>
#include <QApplication>

RankingPanel::RankingPanel(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_controlLayout(nullptr)
    , m_titleLabel(nullptr)
    , m_statusLabel(nullptr)
    , m_lastUpdateLabel(nullptr)
    , m_refreshButton(nullptr)
    , m_rankingTable(nullptr)
    , m_competitionId(-1)
    , m_autoRefreshTimer(nullptr)
    , m_autoRefreshEnabled(true)
    , m_autoRefreshInterval(DEFAULT_AUTO_REFRESH_INTERVAL)
{
    setupUI();
    setupConnections();
    
    qDebug() << "RankingPanel: Initialized";
}

RankingPanel::~RankingPanel()
{
    if (m_autoRefreshTimer) {
        m_autoRefreshTimer->stop();
    }
    qDebug() << "RankingPanel: Destroyed";
}

void RankingPanel::setupUI()
{
    // 主布局
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);
    
    // 控制区域
    setupControls();
    
    // 排名表格
    setupTable();
    
    // 应用样式
    applyTableStyles();
}

void RankingPanel::setupControls()
{
    m_controlLayout = new QHBoxLayout();
    
    // 标题
    m_titleLabel = new QLabel(tr("实时排名"));
    m_titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50;");
    
    // 状态标签
    m_statusLabel = new QLabel(tr("就绪"));
    m_statusLabel->setStyleSheet("color: #27ae60; font-weight: bold;");
    
    // 最后更新时间
    m_lastUpdateLabel = new QLabel(tr("未更新"));
    m_lastUpdateLabel->setStyleSheet("color: #7f8c8d; font-size: 12px;");
    
    // 刷新按钮
    m_refreshButton = new QPushButton(tr("刷新"));
    m_refreshButton->setMaximumWidth(80);
    m_refreshButton->setStyleSheet(R"(
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
        QPushButton:pressed {
            background-color: #21618c;
        }
    )");
    
    // 布局
    m_controlLayout->addWidget(m_titleLabel);
    m_controlLayout->addStretch();
    m_controlLayout->addWidget(m_statusLabel);
    m_controlLayout->addWidget(m_lastUpdateLabel);
    m_controlLayout->addWidget(m_refreshButton);
    
    m_mainLayout->addLayout(m_controlLayout);
}

void RankingPanel::setupTable()
{
    m_rankingTable = new QTableWidget(this);
    
    // 设置列数和标题
    m_rankingTable->setColumnCount(5);
    QStringList headers;
    headers << tr("排名") << tr("姓名") << tr("最佳成绩") << tr("最佳高度失败") << tr("总失败");
    m_rankingTable->setHorizontalHeaderLabels(headers);
    
    // 表格属性
    m_rankingTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_rankingTable->setSelectionMode(QAbstractItemView::SingleSelection);
    m_rankingTable->setAlternatingRowColors(true);
    m_rankingTable->setSortingEnabled(false);
    m_rankingTable->setEditTriggers(QAbstractItemView::NoEditTriggers);
    
    // 列宽设置
    QHeaderView *header = m_rankingTable->horizontalHeader();
    header->setStretchLastSection(true);
    header->resizeSection(RankColumn, 60);
    header->resizeSection(NameColumn, 120);
    header->resizeSection(BestHeightColumn, 100);
    header->resizeSection(FailuresAtBestColumn, 100);
    header->resizeSection(TotalFailuresColumn, 80);
    
    m_mainLayout->addWidget(m_rankingTable);
}

void RankingPanel::setupConnections()
{
    // 自动刷新定时器
    m_autoRefreshTimer = new QTimer(this);
    connect(m_autoRefreshTimer, &QTimer::timeout, this, &RankingPanel::onAutoRefreshTimer);
    
    // 刷新按钮
    connect(m_refreshButton, &QPushButton::clicked, this, &RankingPanel::onRefreshButtonClicked);
    
    // 连接到RulesEngine信号
    RulesEngine *rulesEngine = RulesEngine::instance();
    connect(rulesEngine, &RulesEngine::rankingUpdated, this, &RankingPanel::onRankingUpdated);
    connect(rulesEngine, &RulesEngine::rankingError, this, &RankingPanel::onRankingError);
}

void RankingPanel::applyTableStyles()
{
    m_rankingTable->setStyleSheet(R"(
        QTableWidget {
            gridline-color: #bdc3c7;
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #3498db;
            selection-color: white;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
        }
        QTableWidget::item {
            padding: 8px;
            border: none;
        }
        QHeaderView::section {
            background-color: #34495e;
            color: white;
            padding: 8px;
            border: none;
            font-weight: bold;
        }
    )");
}

void RankingPanel::setCompetitionId(int competitionId)
{
    m_competitionId = competitionId;
    qDebug() << "RankingPanel::setCompetitionId:" << competitionId;
    
    // 清空当前数据
    clearRankingTable();
    
    // 立即刷新
    refreshRanking();
}

void RankingPanel::refreshRanking()
{
    if (m_competitionId <= 0) {
        qWarning() << "RankingPanel::refreshRanking: Invalid competition ID";
        return;
    }
    
    m_statusLabel->setText(tr("更新中..."));
    m_statusLabel->setStyleSheet("color: #f39c12; font-weight: bold;");
    
    // 触发排名计算
    RulesEngine *rulesEngine = RulesEngine::instance();
    rulesEngine->calculateRanking(m_competitionId);
}

void RankingPanel::setAutoRefreshInterval(int intervalMs)
{
    m_autoRefreshInterval = qBound(MIN_AUTO_REFRESH_INTERVAL, intervalMs, MAX_AUTO_REFRESH_INTERVAL);
    
    if (m_autoRefreshTimer && m_autoRefreshTimer->isActive()) {
        m_autoRefreshTimer->setInterval(m_autoRefreshInterval);
    }
    
    qDebug() << "RankingPanel::setAutoRefreshInterval:" << m_autoRefreshInterval;
}

void RankingPanel::setAutoRefreshEnabled(bool enabled)
{
    m_autoRefreshEnabled = enabled;
    
    if (m_autoRefreshTimer) {
        if (enabled && m_competitionId > 0) {
            m_autoRefreshTimer->start(m_autoRefreshInterval);
        } else {
            m_autoRefreshTimer->stop();
        }
    }
    
    qDebug() << "RankingPanel::setAutoRefreshEnabled:" << enabled;
}

void RankingPanel::onRankingUpdated(const QList<RulesEngine::RankingEntry> &rankings)
{
    m_previousRankings = m_currentRankings;
    m_currentRankings = rankings;
    
    updateRankingTable(rankings);
    
    m_statusLabel->setText(tr("已更新"));
    m_statusLabel->setStyleSheet("color: #27ae60; font-weight: bold;");
    
    m_lastUpdateLabel->setText(tr("更新时间: %1").arg(QDateTime::currentDateTime().toString("hh:mm:ss")));
    
    qDebug() << "RankingPanel::onRankingUpdated: Updated with" << rankings.size() << "entries";
}

void RankingPanel::onRankingError(const QString &error)
{
    showErrorMessage(error);
    
    m_statusLabel->setText(tr("错误"));
    m_statusLabel->setStyleSheet("color: #e74c3c; font-weight: bold;");
    
    qWarning() << "RankingPanel::onRankingError:" << error;
}

void RankingPanel::onAutoRefreshTimer()
{
    if (m_autoRefreshEnabled && m_competitionId > 0) {
        refreshRanking();
    }
}

void RankingPanel::onRefreshButtonClicked()
{
    refreshRanking();
}

void RankingPanel::updateRankingTable(const QList<RulesEngine::RankingEntry> &rankings)
{
    m_rankingTable->setRowCount(rankings.size());

    for (int i = 0; i < rankings.size(); ++i) {
        const RulesEngine::RankingEntry &entry = rankings[i];

        // 排名
        QTableWidgetItem *rankItem = new QTableWidgetItem(QString::number(entry.rank));
        rankItem->setTextAlignment(Qt::AlignCenter);
        m_rankingTable->setItem(i, RankColumn, rankItem);

        // 姓名 (需要从数据库查询)
        QTableWidgetItem *nameItem = new QTableWidgetItem(tr("运动员 %1").arg(entry.athleteId));
        m_rankingTable->setItem(i, NameColumn, nameItem);

        // 最佳成绩
        QTableWidgetItem *heightItem = new QTableWidgetItem(
            entry.bestHeight > 0 ? QString("%1cm").arg(entry.bestHeight) : tr("无成绩"));
        heightItem->setTextAlignment(Qt::AlignCenter);
        m_rankingTable->setItem(i, BestHeightColumn, heightItem);

        // 最佳高度失败次数
        QTableWidgetItem *failuresAtBestItem = new QTableWidgetItem(QString::number(entry.failuresAtBest));
        failuresAtBestItem->setTextAlignment(Qt::AlignCenter);
        m_rankingTable->setItem(i, FailuresAtBestColumn, failuresAtBestItem);

        // 总失败次数
        QTableWidgetItem *totalFailuresItem = new QTableWidgetItem(QString::number(entry.totalFailures));
        totalFailuresItem->setTextAlignment(Qt::AlignCenter);
        m_rankingTable->setItem(i, TotalFailuresColumn, totalFailuresItem);
    }

    // 高亮排名变化
    highlightRankingChanges();
}

void RankingPanel::clearRankingTable()
{
    m_rankingTable->setRowCount(0);
    m_currentRankings.clear();
    m_previousRankings.clear();
}

void RankingPanel::showErrorMessage(const QString &error)
{
    // 在表格中显示错误信息
    m_rankingTable->setRowCount(1);
    m_rankingTable->setColumnCount(1);

    QTableWidgetItem *errorItem = new QTableWidgetItem(tr("错误: %1").arg(error));
    errorItem->setTextAlignment(Qt::AlignCenter);
    errorItem->setBackground(QBrush(QColor("#e74c3c")));
    errorItem->setForeground(QBrush(QColor("white")));

    m_rankingTable->setItem(0, 0, errorItem);
    m_rankingTable->setSpan(0, 0, 1, 5);
}

void RankingPanel::highlightRankingChanges()
{
    // 简单实现：高亮前三名
    for (int i = 0; i < qMin(3, m_rankingTable->rowCount()); ++i) {
        QTableWidgetItem *rankItem = m_rankingTable->item(i, RankColumn);
        if (rankItem) {
            switch (i) {
            case 0: // 第一名
                rankItem->setBackground(QBrush(QColor("#f1c40f"))); // 金色
                break;
            case 1: // 第二名
                rankItem->setBackground(QBrush(QColor("#95a5a6"))); // 银色
                break;
            case 2: // 第三名
                rankItem->setBackground(QBrush(QColor("#cd7f32"))); // 铜色
                break;
            }
        }
    }
}
