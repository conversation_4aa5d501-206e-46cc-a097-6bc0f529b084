# Story 1.5: 实现实时排名计算

## Status
Draft

## Epic
Epic 1: MVP核心功能 - 基础跳高计分系统

## Story Points
5

## Priority
High

## Summary
实现实时排名计算功能，确保在每次试跳结果录入后，排名面板能自动刷新并显示准确的实时赛况，遵循官方跳高排名规则。

## User Story
**作为一个** 记分员, **我想要** 在每一次试跳结果被录入后，排名面板能自动刷新, **以便于** 我随时都能看到最准确的实时赛况。

## Acceptance Criteria
1. 任何一次成绩录入后，规则引擎会立刻重新计算所有运动员的排名。
2. 排名规则遵循官方标准。
3. 排名面板准确无误地显示每位选手的名次、姓名和最佳成绩。

## Tasks / Subtasks
- [ ] 实现排名计算核心算法 (AC: 1, 2)
  - [ ] 在RulesEngine中实现calculateRanking()方法
  - [ ] 实现官方跳高排名规则逻辑（最佳成绩 > 最佳高度失败次数 > 总失败次数 > 参赛号码）
  - [ ] 处理并列排名和特殊情况（退赛、淘汰）
- [ ] 集成排名计算到试跳记录流程 (AC: 1)
  - [ ] 修改CompetitionState::recordAttempt()触发排名重计算
  - [ ] 确保排名计算在数据库事务完成后执行
  - [ ] 实现rankingUpdated信号发射
- [ ] 实现排名面板UI更新 (AC: 3)
  - [ ] 创建RankingPanel组件显示实时排名
  - [ ] 连接rankingUpdated信号到UI刷新
  - [ ] 显示排名、姓名、最佳成绩等关键信息
- [ ] 数据库查询优化 (AC: 1)
  - [ ] 利用current_rankings视图进行高效查询
  - [ ] 实现DatabaseManager::getCurrentRanking()方法
  - [ ] 添加必要的数据库索引优化
- [ ] 单元测试实现
  - [ ] 测试基本排名计算逻辑
  - [ ] 测试相同成绩的排名规则
  - [ ] 测试退赛运动员的排名处理
  - [ ] 测试排名更新信号机制

## Dev Notes

### Previous Story Insights
从Story 1.4的实现中学到的关键经验：
- 使用事务确保数据一致性，排名计算应在数据库操作完成后进行
- 信号槽机制实现松耦合，排名更新应通过信号通知UI层
- 单例模式的线程安全实现，确保RulesEngine的线程安全访问
- 错误处理和用户反馈的重要性

### Data Models
**RankingEntry模型** [Source: architecture/data-models.md#RankingEntry]:
```cpp
class RankingEntry {
    int athleteId;
    int rank;
    int bestHeight;
    int totalFailures;
    int failuresAtBest;
    bool operator<(const RankingEntry &other) const; // 排名比较逻辑
};
```

**CompetitionState信号** [Source: architecture/data-models.md#CompetitionState]:
- `rankingUpdated()` - 排名更新时发射
- `currentAthleteChanged(int athleteId)` - 当前运动员变更
- `competitionFinished()` - 比赛结束

### Database Schema
**current_rankings视图** [Source: architecture/database-schema.md#current_rankings]:
```sql
CREATE VIEW current_rankings AS
SELECT *, ROW_NUMBER() OVER (
    PARTITION BY competition_id 
    ORDER BY 
        best_height DESC,
        failures_at_best ASC,
        total_failures ASC,
        number ASC
) as rank
FROM athlete_summary
WHERE status IN ('active', 'finished');
```

**DatabaseManager排名查询方法** [Source: architecture/database-schema.md#DatabaseManager]:
- `QList<RankingEntry> getCurrentRanking(int competitionId)` - 获取当前排名

### File Locations
基于项目结构 [Source: architecture/unified-project-structure.md]:
- 核心排名逻辑: `src/core/rules_engine.h/cpp`
- 比赛状态管理: `src/core/competition_state.h/cpp`
- 数据库操作: `src/persistence/database_manager.h/cpp`
- UI排名面板: `src/ui/ranking_panel.h/cpp`
- 单元测试: `tests/unit/test_rules_engine.cpp`

### Technical Constraints
**技术栈要求** [Source: architecture/tech-stack.md]:
- Qt 6.9.1框架，使用信号槽机制
- C++17标准，利用现代C++特性
- SQLite3数据库，使用预定义视图优化查询
- Qt Test框架进行单元测试

**性能要求**:
- 排名计算必须在试跳录入后立即完成（<100ms）
- 支持大量运动员的排名计算（100+运动员）
- 数据库查询优化，使用索引和视图

### Testing Requirements
**测试策略** [Source: architecture/testing-strategy.md]:
- `testRankingCalculation()` - 基本排名算法测试
- `testRankingWithEqualHeights()` - 相同成绩排名测试
- `testRankingWithRetiredAthletes()` - 退赛运动员处理测试
- `benchmarkRankingCalculation()` - 性能基准测试
- `testGetCurrentRanking()` - 数据库查询测试

**测试数据设置** [Source: architecture/testing-strategy.md]:
- `setupComplexRankingScenario()` - 复杂排名场景测试数据

## Testing

### Testing Standards
**测试文件位置**: `tests/unit/test_rules_engine.cpp`
**测试框架**: Qt Test framework
**测试模式**: 
- 单元测试覆盖核心排名算法
- 集成测试验证完整的排名更新流程
- 性能测试确保大数据集下的响应时间

**具体测试要求**:
- 测试官方排名规则的正确实现
- 验证并列排名的处理逻辑
- 测试退赛和淘汰运动员的排名影响
- 验证排名更新信号的正确发射
- 性能测试确保100+运动员场景下的计算效率

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-07 | 1.0 | Initial story creation | Bob (Scrum Master) |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

## QA Results
*This section will be populated by the QA agent after testing*
