# Tests CMakeLists.txt
# Unit tests for High Jump Competition Management System

# Find Qt Test module
find_package(Qt6 REQUIRED COMPONENTS Test)

# Enable Qt Test
qt_add_executable(test_database_manager
    unit/test_database_manager.cpp
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
)

qt_add_executable(test_config_manager
    unit/test_config_manager.cpp
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
)

qt_add_executable(test_api_client
    unit/test_api_client.cpp
    "${CMAKE_SOURCE_DIR}/src/api/api_client.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
)

qt_add_executable(test_competition_selection_view
    unit/test_competition_selection_view.cpp
    "${CMAKE_SOURCE_DIR}/src/ui/competition_selection_view.cpp"
)

qt_add_executable(test_scoring_view
    unit/test_scoring_view.cpp
    "${CMAKE_SOURCE_DIR}/src/ui/scoring_view.cpp"
)

qt_add_executable(test_rules_engine
    unit/test_rules_engine.cpp
    "${CMAKE_SOURCE_DIR}/src/core/rules_engine.cpp"
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
)

# Enable automatic MOC processing for all test executables
set_target_properties(test_database_manager test_config_manager test_api_client test_competition_selection_view test_scoring_view test_rules_engine PROPERTIES
    AUTOMOC ON
)

# Link Qt libraries
target_link_libraries(test_database_manager PRIVATE
    Qt6::Core
    Qt6::Sql
    Qt6::Test
)

target_link_libraries(test_config_manager PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

target_link_libraries(test_api_client PRIVATE
    Qt6::Core
    Qt6::Network
    Qt6::Widgets
    Qt6::Test
)

target_link_libraries(test_competition_selection_view PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

target_link_libraries(test_scoring_view PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Test
)

target_link_libraries(test_rules_engine PRIVATE
    Qt6::Core
    Qt6::Sql
    Qt6::Test
)

# Include directories
target_include_directories(test_database_manager PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(test_config_manager PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(test_api_client PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(test_competition_selection_view PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(test_scoring_view PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

target_include_directories(test_rules_engine PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Set output directory for tests
set_target_properties(test_database_manager test_config_manager test_api_client test_competition_selection_view test_scoring_view test_rules_engine PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Deploy Qt plugins for tests (Windows)
if(WIN32)
    # Find Qt installation path
    get_target_property(QT_QMAKE_EXECUTABLE Qt6::qmake IMPORTED_LOCATION)
    get_filename_component(QT_WINDEPLOYQT_EXECUTABLE ${QT_QMAKE_EXECUTABLE} PATH)
    set(QT_WINDEPLOYQT_EXECUTABLE "${QT_WINDEPLOYQT_EXECUTABLE}/windeployqt.exe")

    if(EXISTS ${QT_WINDEPLOYQT_EXECUTABLE})
        # Deploy Qt for each test executable
        foreach(test_target test_database_manager test_config_manager test_api_client test_competition_selection_view test_scoring_view test_rules_engine)
            add_custom_command(TARGET ${test_target} POST_BUILD
                COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --sql $<TARGET_FILE:${test_target}>
                COMMENT "Deploying Qt libraries and plugins for ${test_target}")
        endforeach()
    endif()
endif()

# Add tests to CTest
add_test(NAME DatabaseManagerTest COMMAND test_database_manager)
add_test(NAME ConfigManagerTest COMMAND test_config_manager)
add_test(NAME ApiClientTest COMMAND test_api_client)
add_test(NAME CompetitionSelectionViewTest COMMAND test_competition_selection_view)
add_test(NAME ScoringViewTest COMMAND test_scoring_view)
add_test(NAME RulesEngineTest COMMAND test_rules_engine)

# Sprint 2 E2E Tests - Simplified Version
qt_add_executable(test_e2e_simple
    integration/test_e2e_simple.cpp
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/api/api_client.cpp"
)

# Enable automatic MOC processing for the E2E test
set_target_properties(test_e2e_simple PROPERTIES
    AUTOMOC ON
)

target_link_libraries(test_e2e_simple PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Network
    Qt6::Test
)

target_include_directories(test_e2e_simple PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Set output directory for E2E test
set_target_properties(test_e2e_simple PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Deploy Qt for E2E test (Windows)
if(WIN32 AND EXISTS ${QT_WINDEPLOYQT_EXECUTABLE})
    add_custom_command(TARGET test_e2e_simple POST_BUILD
        COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --sql $<TARGET_FILE:test_e2e_simple>
        COMMENT "Deploying Qt libraries and plugins for test_e2e_simple")
endif()

add_test(NAME E2ESimpleTest COMMAND test_e2e_simple)

# Application Startup Integration Test
qt_add_executable(test_application_startup
    integration/test_application_startup.cpp
    "${CMAKE_SOURCE_DIR}/src/ui/main_window.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/competition_selection_view.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/view_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/scoring_view.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/competition.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/athlete.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/jump_attempt.cpp"
    "${CMAKE_SOURCE_DIR}/src/api/api_client.cpp"
    "${CMAKE_SOURCE_DIR}/src/api/competition_dto.cpp"
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/core/jump_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/athlete_dialog.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/report_dialog.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/theme_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/performance_monitor.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/report_generator.cpp"
)

# Enable automatic MOC processing for the startup test
set_target_properties(test_application_startup PROPERTIES
    AUTOMOC ON
)

target_link_libraries(test_application_startup PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Network
    Qt6::Test
    Qt6::PrintSupport
)

target_include_directories(test_application_startup PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Set output directory for startup test
set_target_properties(test_application_startup PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Deploy Qt for startup test (Windows)
if(WIN32 AND EXISTS ${QT_WINDEPLOYQT_EXECUTABLE})
    add_custom_command(TARGET test_application_startup POST_BUILD
        COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --sql $<TARGET_FILE:test_application_startup>
        COMMENT "Deploying Qt libraries and plugins for test_application_startup")
endif()

add_test(NAME ApplicationStartupTest COMMAND test_application_startup)

# AthleteTableModel Unit Test
qt_add_executable(test_athlete_table_model
    unit/test_athlete_table_model.cpp
    "${CMAKE_SOURCE_DIR}/src/models/athlete_table_model.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/athlete.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/competition.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/jump_attempt.cpp"
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
)

# Enable automatic MOC processing for the athlete table model test
set_target_properties(test_athlete_table_model PROPERTIES
    AUTOMOC ON
)

target_link_libraries(test_athlete_table_model PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Test
)

target_include_directories(test_athlete_table_model PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Set output directory for athlete table model test
set_target_properties(test_athlete_table_model PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Deploy Qt for athlete table model test (Windows)
if(WIN32 AND EXISTS ${QT_WINDEPLOYQT_EXECUTABLE})
    add_custom_command(TARGET test_athlete_table_model POST_BUILD
        COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --sql $<TARGET_FILE:test_athlete_table_model>
        COMMENT "Deploying Qt libraries and plugins for test_athlete_table_model")
endif()

add_test(NAME AthleteTableModelTest COMMAND test_athlete_table_model)

# AthleteDelegate Unit Test
qt_add_executable(test_athlete_delegate
    unit/test_athlete_delegate.cpp
    "${CMAKE_SOURCE_DIR}/src/ui/athlete_delegate.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/athlete_table_model.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/athlete.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/competition.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/jump_attempt.cpp"
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
)

# Enable automatic MOC processing for the athlete delegate test
set_target_properties(test_athlete_delegate PROPERTIES
    AUTOMOC ON
)

target_link_libraries(test_athlete_delegate PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Test
)

target_include_directories(test_athlete_delegate PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Set output directory for athlete delegate test
set_target_properties(test_athlete_delegate PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Deploy Qt for athlete delegate test (Windows)
if(WIN32 AND EXISTS ${QT_WINDEPLOYQT_EXECUTABLE})
    add_custom_command(TARGET test_athlete_delegate POST_BUILD
        COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --sql $<TARGET_FILE:test_athlete_delegate>
        COMMENT "Deploying Qt libraries and plugins for test_athlete_delegate")
endif()

add_test(NAME AthleteDelegateTest COMMAND test_athlete_delegate)

# ShortcutManager Unit Test
qt_add_executable(test_shortcut_manager
    unit/test_shortcut_manager.cpp
    "${CMAKE_SOURCE_DIR}/src/ui/shortcut_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/athlete_table_model.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/athlete.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/competition.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/jump_attempt.cpp"
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
)

# Enable automatic MOC processing for the shortcut manager test
set_target_properties(test_shortcut_manager PROPERTIES
    AUTOMOC ON
)

target_link_libraries(test_shortcut_manager PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Sql
    Qt6::Test
)

target_include_directories(test_shortcut_manager PRIVATE
    ${CMAKE_SOURCE_DIR}/src
)

# Set output directory for shortcut manager test
set_target_properties(test_shortcut_manager PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Deploy Qt for shortcut manager test (Windows)
if(WIN32 AND EXISTS ${QT_WINDEPLOYQT_EXECUTABLE})
    add_custom_command(TARGET test_shortcut_manager POST_BUILD
        COMMAND ${QT_WINDEPLOYQT_EXECUTABLE} --sql $<TARGET_FILE:test_shortcut_manager>
        COMMENT "Deploying Qt libraries and plugins for test_shortcut_manager")
endif()

add_test(NAME ShortcutManagerTest COMMAND test_shortcut_manager)

# Performance Test
qt_add_executable(test_large_scale_performance
    performance/test_large_scale_performance.cpp
    "${CMAKE_SOURCE_DIR}/src/models/athlete_table_model.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/athlete_delegate.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/scoring_view.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/shortcut_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/ranking_calculator.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/competition_state.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/athlete.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/competition.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/jump_attempt.cpp"
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
)

set_target_properties(test_large_scale_performance PROPERTIES AUTOMOC ON)
target_link_libraries(test_large_scale_performance PRIVATE Qt6::Core Qt6::Widgets Qt6::Sql Qt6::Test)
target_include_directories(test_large_scale_performance PRIVATE ${CMAKE_SOURCE_DIR}/src)
set_target_properties(test_large_scale_performance PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# UI/UX Usability Test
qt_add_executable(test_ui_ux_usability
    ui/test_ui_ux_usability.cpp
    "${CMAKE_SOURCE_DIR}/src/ui/scoring_view.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/athlete_table_model.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/athlete_delegate.cpp"
    "${CMAKE_SOURCE_DIR}/src/ui/shortcut_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/athlete.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/competition.cpp"
    "${CMAKE_SOURCE_DIR}/src/models/jump_attempt.cpp"
    "${CMAKE_SOURCE_DIR}/src/persistence/database_manager.cpp"
    "${CMAKE_SOURCE_DIR}/src/utils/config_manager.cpp"
)

set_target_properties(test_ui_ux_usability PROPERTIES AUTOMOC ON)
target_link_libraries(test_ui_ux_usability PRIVATE Qt6::Core Qt6::Widgets Qt6::Sql Qt6::Test)
target_include_directories(test_ui_ux_usability PRIVATE ${CMAKE_SOURCE_DIR}/src)
set_target_properties(test_ui_ux_usability PROPERTIES RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

add_test(NAME LargeScalePerformanceTest COMMAND test_large_scale_performance)
add_test(NAME UIUXUsabilityTest COMMAND test_ui_ux_usability)