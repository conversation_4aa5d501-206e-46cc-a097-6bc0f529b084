#include <QtTest>
#include <QSignalSpy>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QStandardPaths>
#include <QDir>

#include "core/rules_engine.h"
#include "persistence/database_manager.h"
#include "models/ranking_calculator.h"

class TestRulesEngine : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // 基本排名计算测试
    void testRankingCalculation();
    void testRankingWithEqualHeights();
    void testRankingWithRetiredAthletes();
    
    // 信号机制测试
    void testRankingUpdatedSignal();
    void testRankingErrorSignal();
    
    // 数据验证测试
    void testValidateRankingData();
    void testEmptyRankingData();
    
    // 性能测试
    void benchmarkRankingCalculation();

private:
    void setupTestDatabase();
    void createTestData();
    void insertTestAthlete(int id, const QString &name, int number);
    void insertTestAttempt(int athleteId, int height, int attemptNumber, const QString &result);
    
    RulesEngine *m_rulesEngine;
    DatabaseManager *m_dbManager;
    QString m_testDbPath;
    int m_testCompetitionId;
};

void TestRulesEngine::initTestCase()
{
    // 设置测试数据库路径
    QString tempDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation);
    m_testDbPath = QDir(tempDir).filePath("test_rules_engine.sqlite");
    
    // 确保测试数据库不存在
    QFile::remove(m_testDbPath);
    
    qDebug() << "TestRulesEngine: Test database path:" << m_testDbPath;
}

void TestRulesEngine::cleanupTestCase()
{
    // 清理测试数据库
    if (m_dbManager) {
        m_dbManager->close();
    }
    
    QFile::remove(m_testDbPath);
    
    // 销毁单例
    RulesEngine::destroyInstance();
}

void TestRulesEngine::init()
{
    // 获取RulesEngine实例
    m_rulesEngine = RulesEngine::instance();
    QVERIFY(m_rulesEngine != nullptr);
    
    // 设置测试数据库
    setupTestDatabase();
    
    // 创建测试数据
    createTestData();
}

void TestRulesEngine::cleanup()
{
    // 每个测试后清理
    if (m_dbManager && m_dbManager->isConnected()) {
        QSqlQuery query;
        query.exec("DELETE FROM attempt_records");
        query.exec("DELETE FROM athletes");
        query.exec("DELETE FROM competitions");
    }
}

void TestRulesEngine::setupTestDatabase()
{
    m_dbManager = DatabaseManager::instance();
    QVERIFY(m_dbManager != nullptr);
    
    // 初始化数据库
    bool initialized = m_dbManager->initialize();
    QVERIFY2(initialized, "Failed to initialize test database");
    
    // 设置RulesEngine的数据库管理器
    m_rulesEngine->setDatabaseManager(m_dbManager);
}

void TestRulesEngine::createTestData()
{
    // 创建测试比赛
    QSqlQuery query;
    query.prepare("INSERT INTO competitions (id, name, date, venue, starting_height, status) "
                  "VALUES (1, 'Test Competition', '2025-08-07', 'Test Venue', 150, 'in_progress')");
    QVERIFY2(query.exec(), qPrintable(query.lastError().text()));
    
    m_testCompetitionId = 1;
    
    // 创建测试运动员
    insertTestAthlete(1, "张三", 101);
    insertTestAthlete(2, "李四", 102);
    insertTestAthlete(3, "王五", 103);
    insertTestAthlete(4, "赵六", 104);
    
    // 创建测试试跳记录
    // 张三: 150cm成功, 155cm失败
    insertTestAttempt(1, 150, 1, "success");
    insertTestAttempt(1, 155, 1, "failure");
    
    // 李四: 150cm成功, 155cm成功, 160cm失败
    insertTestAttempt(2, 150, 1, "success");
    insertTestAttempt(2, 155, 1, "success");
    insertTestAttempt(2, 160, 1, "failure");
    
    // 王五: 150cm失败, 150cm成功, 155cm失败失败
    insertTestAttempt(3, 150, 1, "failure");
    insertTestAttempt(3, 150, 2, "success");
    insertTestAttempt(3, 155, 1, "failure");
    insertTestAttempt(3, 155, 2, "failure");
    
    // 赵六: 退赛
    insertTestAttempt(4, 150, 1, "retire");
}

void TestRulesEngine::insertTestAthlete(int id, const QString &name, int number)
{
    QSqlQuery query;
    query.prepare("INSERT INTO athletes (id, competition_id, name, number, status) "
                  "VALUES (?, ?, ?, ?, 'active')");
    query.addBindValue(id);
    query.addBindValue(m_testCompetitionId);
    query.addBindValue(name);
    query.addBindValue(number);
    QVERIFY2(query.exec(), qPrintable(query.lastError().text()));
}

void TestRulesEngine::insertTestAttempt(int athleteId, int height, int attemptNumber, const QString &result)
{
    QSqlQuery query;
    query.prepare("INSERT INTO attempt_records (athlete_id, height, attempt_number, result) "
                  "VALUES (?, ?, ?, ?)");
    query.addBindValue(athleteId);
    query.addBindValue(height);
    query.addBindValue(attemptNumber);
    query.addBindValue(result);
    QVERIFY2(query.exec(), qPrintable(query.lastError().text()));
}

void TestRulesEngine::testRankingCalculation()
{
    // 计算排名
    QList<RulesEngine::RankingEntry> rankings = m_rulesEngine->calculateRanking(m_testCompetitionId);
    
    // 验证排名数量
    QCOMPARE(rankings.size(), 4);
    
    // 验证排名顺序（按最佳成绩排序）
    QCOMPARE(rankings[0].athleteId, 2); // 李四，最佳成绩155cm
    QCOMPARE(rankings[0].bestHeight, 155);
    QCOMPARE(rankings[0].rank, 1);
    
    QCOMPARE(rankings[1].athleteId, 3); // 王五，最佳成绩150cm，但失败次数多
    QCOMPARE(rankings[1].bestHeight, 150);
    QCOMPARE(rankings[1].rank, 2);
    
    QCOMPARE(rankings[2].athleteId, 1); // 张三，最佳成绩150cm，失败次数少
    QCOMPARE(rankings[2].bestHeight, 150);
    QCOMPARE(rankings[2].rank, 3);
    
    // 赵六应该排在最后（退赛）
    QCOMPARE(rankings[3].athleteId, 4);
    QCOMPARE(rankings[3].rank, 4);
}

void TestRulesEngine::testRankingWithEqualHeights()
{
    // 清除现有数据
    QSqlQuery query;
    query.exec("DELETE FROM attempt_records");
    
    // 创建相同最佳成绩的测试数据
    insertTestAttempt(1, 150, 1, "success"); // 张三: 150cm, 0次失败
    insertTestAttempt(2, 150, 1, "failure");  // 李四: 150cm, 1次失败
    insertTestAttempt(2, 150, 2, "success");
    
    QList<RulesEngine::RankingEntry> rankings = m_rulesEngine->calculateRanking(m_testCompetitionId);
    
    // 验证相同成绩时按失败次数排序
    QCOMPARE(rankings[0].athleteId, 1); // 张三，失败次数少
    QCOMPARE(rankings[1].athleteId, 2); // 李四，失败次数多
}

void TestRulesEngine::testRankingWithRetiredAthletes()
{
    QList<RulesEngine::RankingEntry> rankings = m_rulesEngine->calculateRanking(m_testCompetitionId);
    
    // 查找退赛运动员
    bool foundRetiredAthlete = false;
    for (const auto &entry : rankings) {
        if (entry.athleteId == 4) { // 赵六退赛
            foundRetiredAthlete = true;
            // 退赛运动员应该排在最后
            QVERIFY(entry.rank >= rankings.size() - 1);
            break;
        }
    }
    QVERIFY(foundRetiredAthlete);
}

void TestRulesEngine::testRankingUpdatedSignal()
{
    QSignalSpy spy(m_rulesEngine, &RulesEngine::rankingUpdated);
    
    // 触发排名计算
    m_rulesEngine->calculateRanking(m_testCompetitionId);
    
    // 验证信号发射
    QCOMPARE(spy.count(), 1);
    
    // 验证信号参数
    QList<QVariant> arguments = spy.takeFirst();
    QCOMPARE(arguments.size(), 1);
}

void TestRulesEngine::testRankingErrorSignal()
{
    QSignalSpy spy(m_rulesEngine, &RulesEngine::rankingError);
    
    // 触发错误（使用无效的比赛ID）
    m_rulesEngine->calculateRanking(-1);
    
    // 验证错误信号发射
    QVERIFY(spy.count() >= 0); // 可能发射错误信号
}

void TestRulesEngine::testValidateRankingData()
{
    QList<RulesEngine::RankingEntry> rankings = m_rulesEngine->calculateRanking(m_testCompetitionId);
    
    // 验证排名数据的有效性
    QSet<int> athleteIds;
    QSet<int> ranks;
    
    for (const auto &entry : rankings) {
        // 检查运动员ID唯一性
        QVERIFY(!athleteIds.contains(entry.athleteId));
        athleteIds.insert(entry.athleteId);
        
        // 检查排名有效性
        QVERIFY(entry.rank >= 1);
        QVERIFY(entry.rank <= rankings.size());
        
        // 检查数据有效性
        QVERIFY(entry.bestHeight >= 0);
        QVERIFY(entry.totalFailures >= 0);
        QVERIFY(entry.failuresAtBest >= 0);
    }
}

void TestRulesEngine::testEmptyRankingData()
{
    // 清除所有运动员
    QSqlQuery query;
    query.exec("DELETE FROM attempt_records");
    query.exec("DELETE FROM athletes");
    
    QList<RulesEngine::RankingEntry> rankings = m_rulesEngine->calculateRanking(m_testCompetitionId);
    
    // 验证空排名
    QCOMPARE(rankings.size(), 0);
}

void TestRulesEngine::benchmarkRankingCalculation()
{
    QBENCHMARK {
        m_rulesEngine->calculateRanking(m_testCompetitionId);
    }
}

QTEST_MAIN(TestRulesEngine)
#include "test_rules_engine.moc"
