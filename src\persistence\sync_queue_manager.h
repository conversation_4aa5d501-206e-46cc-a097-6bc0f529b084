#ifndef SYNC_QUEUE_MANAGER_H
#define SYNC_QUEUE_MANAGER_H

#include <QObject>
#include <QJsonObject>
#include <QDateTime>
#include <QTimer>
#include <QMutex>

class DatabaseManager;

/**
 * @brief 同步队列管理器 - 管理离线数据同步
 * 
 * 这个类负责管理需要同步到服务器的操作队列，实现离线优先的数据同步机制。
 * 所有本地数据变更都会被添加到同步队列中，在网络恢复时自动同步到服务器。
 */
class SyncQueueManager : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 操作类型枚举
     */
    enum OperationType {
        CreateAttempt = 0,      // 创建试跳记录
        UpdateAthlete = 1,      // 更新运动员状态
        UpdateCompetition = 2   // 更新比赛状态
    };

    /**
     * @brief 同步状态枚举
     */
    enum SyncStatus {
        Pending = 0,        // 待同步
        InProgress = 1,     // 同步中
        Completed = 2,      // 已完成
        Failed = 3          // 同步失败
    };

    /**
     * @brief 同步队列条目结构
     */
    struct SyncQueueEntry {
        int id;
        OperationType operationType;
        QJsonObject data;
        SyncStatus status;
        int retryCount;
        QDateTime createdAt;
        QDateTime lastAttemptAt;
        QDateTime completedAt;
        QString errorMessage;
    };

    static SyncQueueManager* instance();
    static void destroyInstance();
    
    // 队列操作
    /**
     * @brief 添加操作到同步队列
     * @param operationType 操作类型
     * @param data 操作数据
     * @return 队列条目ID，失败返回-1
     */
    int addOperation(OperationType operationType, const QJsonObject &data);
    
    /**
     * @brief 获取待同步的操作列表
     * @return 待同步操作列表
     */
    QList<SyncQueueEntry> getPendingOperations();
    
    /**
     * @brief 标记操作为已完成
     * @param entryId 队列条目ID
     * @return 是否成功
     */
    bool markAsCompleted(int entryId);
    
    /**
     * @brief 标记操作为失败
     * @param entryId 队列条目ID
     * @param errorMessage 错误信息
     * @return 是否成功
     */
    bool markAsFailed(int entryId, const QString &errorMessage);
    
    /**
     * @brief 更新操作状态
     * @param entryId 队列条目ID
     * @param status 新状态
     * @return 是否成功
     */
    bool updateStatus(int entryId, SyncStatus status);
    
    // 队列统计
    /**
     * @brief 获取待同步操作数量
     * @return 待同步操作数量
     */
    int pendingCount() const;
    
    /**
     * @brief 获取失败操作数量
     * @return 失败操作数量
     */
    int failedCount() const;
    
    /**
     * @brief 获取最后同步时间
     * @return 最后同步时间
     */
    QDateTime lastSyncTime() const;
    
    // 队列管理
    /**
     * @brief 清理已完成的操作
     * @param olderThanDays 清理多少天前的记录
     * @return 清理的记录数量
     */
    int cleanupCompleted(int olderThanDays = 7);
    
    /**
     * @brief 重置失败的操作为待同步状态
     * @return 重置的操作数量
     */
    int retryFailedOperations();

signals:
    /**
     * @brief 操作添加到队列
     * @param entry 队列条目
     */
    void operationAdded(const SyncQueueEntry &entry);
    
    /**
     * @brief 操作完成
     * @param entryId 队列条目ID
     */
    void operationCompleted(int entryId);
    
    /**
     * @brief 操作失败
     * @param entryId 队列条目ID
     * @param errorMessage 错误信息
     */
    void operationFailed(int entryId, const QString &errorMessage);
    
    /**
     * @brief 队列状态变化
     * @param pendingCount 待同步数量
     * @param failedCount 失败数量
     */
    void queueStatusChanged(int pendingCount, int failedCount);

private slots:
    void onDatabaseError(const QString &error);

private:
    explicit SyncQueueManager(QObject *parent = nullptr);
    ~SyncQueueManager();
    
    // 数据库操作
    bool createSyncQueueEntry(const SyncQueueEntry &entry);
    SyncQueueEntry getSyncQueueEntry(int entryId);
    bool updateSyncQueueEntry(const SyncQueueEntry &entry);
    
    // 工具方法
    QString operationTypeToString(OperationType type) const;
    OperationType stringToOperationType(const QString &str) const;
    QString syncStatusToString(SyncStatus status) const;
    SyncStatus stringToSyncStatus(const QString &str) const;
    
    // 成员变量
    DatabaseManager *m_dbManager;
    QMutex m_mutex;
    
    static SyncQueueManager *s_instance;
};

#endif // SYNC_QUEUE_MANAGER_H
