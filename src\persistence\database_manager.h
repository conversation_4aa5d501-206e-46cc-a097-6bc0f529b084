#ifndef DATABASE_MANAGER_H
#define DATABASE_MANAGER_H

#include <QObject>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <QString>
#include <QMutex>

class DatabaseManager : public QObject
{
    Q_OBJECT
    
    // Friend class declarations for testing
    friend class TestDatabaseManager;
    friend class TestE2ESimple;

public:
    static DatabaseManager* instance();
    
    // Database lifecycle
    bool initialize();
    bool isConnected() const;
    QString getDatabasePath() const;
    void close();
    
    // Schema management
    int getCurrentSchemaVersion();
    bool migrateSchema();
    
    // Transaction management
    bool beginTransaction();
    bool commitTransaction();
    bool rollbackTransaction();
    
    // Error handling
    QString lastError() const;

    // Attempt record operations
    bool recordAttempt(int athleteId, int height, int attemptNumber, const QString &result);

    // Ranking operations
    QSqlQuery getCurrentRanking(int competitionId);

    // Sync queue operations
    bool insertSyncQueueEntry(const QString &operationType, const QString &dataJson,
                             const QString &status, int retryCount, const QString &createdAt);
    bool updateSyncQueueEntry(int id, const QString &status, const QString &lastAttemptAt,
                             const QString &completedAt = QString(), const QString &errorMessage = QString());
    QSqlQuery executeSyncQueueQuery(const QString &sql, const QVariantList &params = QVariantList());

signals:
    void databaseError(const QString &message);
    void schemaVersionChanged(int newVersion);

private slots:
    void onDatabaseError(const QString &error);

private:
    explicit DatabaseManager(QObject *parent = nullptr);
    ~DatabaseManager();
    
    // Initialization helpers
    bool createDatabaseFile();
    bool createTables();
    bool createIndexes();
    void setupErrorHandling();
    
    // Schema creation methods
    bool createCompetitionsTable();
    bool createAthletesTable();
    bool createHeightSettingsTable();
    bool createAttemptRecordsTable();
    bool createSyncQueueTable();
    bool createSchemaVersionTable();
    
    // Schema validation
    bool validateTableSchema(const QString &tableName);
    bool tableExists(const QString &tableName);
    
    // Database connection
    bool connectToDatabase();
    void logDatabaseError(const QString &operation, const QSqlError &error);
    
    // Member variables
    QSqlDatabase m_database;
    QString m_databasePath;
    QString m_lastError;
    QMutex m_mutex;
    bool m_isInitialized;
    
    static DatabaseManager* s_instance;
    static const int CURRENT_SCHEMA_VERSION = 1;
    static const QString DATABASE_CONNECTION_NAME;
};

#endif // DATABASE_MANAGER_H