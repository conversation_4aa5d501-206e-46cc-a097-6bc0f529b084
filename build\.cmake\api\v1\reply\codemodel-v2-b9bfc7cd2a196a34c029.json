{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-Debug-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-ee9b4b0d4af9421b9795.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-Debug-4fedd1e22d46271b8a78.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-Debug-773d90a782a0f314f162.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-Debug-3ea849b000cd76a56366.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-Debug-6ba57d3236a2ae9eab8a.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-Debug-7369a76029a25dbe50c6.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-Debug-caef603dfaea57b89708.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-Debug-e3bc444737c477220ed2.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-Debug-1ae1bf720ce57c577ff6.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-Debug-2b2d45db88dc64e70c7c.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-Debug-773c997c5c90a1a8845c.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_rules_engine::@a44f0ac069e85531cdee", "jsonFile": "target-test_rules_engine-Debug-b6a1b2b161d3ad457c65.json", "name": "test_rules_engine", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-Debug-af3154b4055bf8b685cb.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-Debug-6f825b46c10ccc49b709.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-Debug-357c9e7eaf85ffbc6c8b.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-Release-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "name": "Release", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-ee9b4b0d4af9421b9795.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-Release-ce0f188764d8666823b3.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-Release-87d5d7592577d8a3e771.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-Release-c892d06821bcbd4b80e1.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-Release-f687e105b7198bc3d1a5.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-Release-93d50a7647573c7f994b.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-Release-38d680a35fe860811fe9.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-Release-fba74c0685eb0c791c29.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-Release-0b480413eb39f867a37e.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-Release-fb747a475786c97d88e9.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-Release-27d22aeb600ce6633cc1.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_rules_engine::@a44f0ac069e85531cdee", "jsonFile": "target-test_rules_engine-Release-67be1787761b29bd86a9.json", "name": "test_rules_engine", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-Release-4ba77e64356fea81cf6a.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-Release-ae978e45e156a4cd77c7.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-Release-d78980cfca06bb350bd0.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-MinSizeRel-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "name": "MinSizeRel", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-ee9b4b0d4af9421b9795.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-MinSizeRel-1a74be67d70881c27f36.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-MinSizeRel-f5db0c55273179b4bcb0.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-MinSizeRel-0a816d41c43977685162.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-MinSizeRel-d8189862941d42b5c07a.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-MinSizeRel-ffa92b8ac2cfcc669dca.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-MinSizeRel-f0bf8646369e11ff8e51.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-MinSizeRel-592563698bdca572a856.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-MinSizeRel-9822326efbac30e5e32f.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-MinSizeRel-c104959611d1653f6066.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-MinSizeRel-24996a72fb6e36e8cdef.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_rules_engine::@a44f0ac069e85531cdee", "jsonFile": "target-test_rules_engine-MinSizeRel-7ef5e5a785a447527d7e.json", "name": "test_rules_engine", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-MinSizeRel-9a1873240d522b7643d3.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-MinSizeRel-26c52f2d36bdbc3c3535.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-MinSizeRel-58812dab4bdbd026d349.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}, {"build": "tests", "jsonFile": "directory-tests-RelWithDebInfo-70e4927a02b591d5c1f8.json", "minimumCMakeVersion": {"string": "3.16"}, "parentIndex": 0, "projectIndex": 0, "source": "tests", "targetIndexes": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0, 1], "name": "high-jump-scorer", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-ee9b4b0d4af9421b9795.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-a5bb5bebf684af5a7592.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "jsonFile": "target-high-jump-scorer-RelWithDebInfo-76f7a85953030cf9a4d8.json", "name": "high-jump-scorer", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_api_client::@a44f0ac069e85531cdee", "jsonFile": "target-test_api_client-RelWithDebInfo-2e5e0eb6c7c114836d83.json", "name": "test_api_client", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_application_startup::@a44f0ac069e85531cdee", "jsonFile": "target-test_application_startup-RelWithDebInfo-a89e1a1164d1ad547a95.json", "name": "test_application_startup", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_delegate::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_delegate-RelWithDebInfo-8a324f4566b1e296c1ed.json", "name": "test_athlete_delegate", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_athlete_table_model::@a44f0ac069e85531cdee", "jsonFile": "target-test_athlete_table_model-RelWithDebInfo-59a91b91b4483b560531.json", "name": "test_athlete_table_model", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_competition_selection_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_competition_selection_view-RelWithDebInfo-6049bd11698c035a415e.json", "name": "test_competition_selection_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_config_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_config_manager-RelWithDebInfo-32bcc869af88ff916ee3.json", "name": "test_config_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_database_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_database_manager-RelWithDebInfo-77a327fc0e1b4fccbdd9.json", "name": "test_database_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_e2e_simple::@a44f0ac069e85531cdee", "jsonFile": "target-test_e2e_simple-RelWithDebInfo-6c0046d1bd0ac32967e0.json", "name": "test_e2e_simple", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_large_scale_performance::@a44f0ac069e85531cdee", "jsonFile": "target-test_large_scale_performance-RelWithDebInfo-8ad525793d6fca8cf43a.json", "name": "test_large_scale_performance", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_rules_engine::@a44f0ac069e85531cdee", "jsonFile": "target-test_rules_engine-RelWithDebInfo-7a598d3fe2a60df7fb2f.json", "name": "test_rules_engine", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_scoring_view::@a44f0ac069e85531cdee", "jsonFile": "target-test_scoring_view-RelWithDebInfo-6f4ec91c070157f61eb5.json", "name": "test_scoring_view", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_shortcut_manager::@a44f0ac069e85531cdee", "jsonFile": "target-test_shortcut_manager-RelWithDebInfo-26d4b4139c748522572a.json", "name": "test_shortcut_manager", "projectIndex": 0}, {"directoryIndex": 1, "id": "test_ui_ux_usability::@a44f0ac069e85531cdee", "jsonFile": "target-test_ui_ux_usability-RelWithDebInfo-89af39bf2d400199bb64.json", "name": "test_ui_ux_usability", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/PROJECT/HighJump/build", "source": "C:/PROJECT/HighJump"}, "version": {"major": 2, "minor": 8}}