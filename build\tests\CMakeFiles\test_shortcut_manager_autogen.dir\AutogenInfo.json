{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen", "CMAKE_BINARY_DIR": "C:/PROJECT/HighJump/build", "CMAKE_CURRENT_BINARY_DIR": "C:/PROJECT/HighJump/build/tests", "CMAKE_CURRENT_SOURCE_DIR": "C:/PROJECT/HighJump/tests", "CMAKE_EXECUTABLE": "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["C:/PROJECT/HighJump/tests/CMakeLists.txt", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtInstallPaths.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Targets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtFeature.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CheckCXXCompilerFlag.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtFeatureCommon.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Dependencies.cmake"], "CMAKE_SOURCE_DIR": "C:/PROJECT/HighJump", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["C:/PROJECT/HighJump/src/models/athlete.h", "MU", "PYZBKSLS27/moc_athlete.cpp", null], ["C:/PROJECT/HighJump/src/models/athlete_table_model.h", "MU", "PYZBKSLS27/moc_athlete_table_model.cpp", null], ["C:/PROJECT/HighJump/src/models/competition.h", "MU", "PYZBKSLS27/moc_competition.cpp", null], ["C:/PROJECT/HighJump/src/models/jump_attempt.h", "MU", "PYZBKSLS27/moc_jump_attempt.cpp", null], ["C:/PROJECT/HighJump/src/persistence/database_manager.h", "MU", "CCISZRPI35/moc_database_manager.cpp", null], ["C:/PROJECT/HighJump/src/ui/shortcut_manager.h", "MU", "RF5KSEWXUK/moc_shortcut_manager.cpp", null], ["C:/PROJECT/HighJump/src/utils/config_manager.h", "MU", "7KRDZL6R5E/moc_config_manager.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/include", "INCLUDE_DIR_Debug": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/include_Release", "MOC_COMPILATION_FILE": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "C:/PROJECT/HighJump/build/tests/test_shortcut_manager_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_SQL_LIB", "QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\"", "QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\"", "QT_TESTLIB_LIB", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_MinSizeRel": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SQL_LIB", "QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\"", "QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\"", "QT_TESTLIB_LIB", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_RelWithDebInfo": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SQL_LIB", "QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\"", "QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\"", "QT_TESTLIB_LIB", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_Release": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_SQL_LIB", "QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\"", "QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\"", "QT_TESTLIB_LIB", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["C:/PROJECT/HighJump/src", "C:/Qt/install-x64/include/QtCore", "C:/Qt/install-x64/include", "C:/Qt/install-x64/mkspecs/win32-arm64-msvc", "C:/Qt/install-x64/include/QtWidgets", "C:/Qt/install-x64/include/QtGui", "C:/Qt/install-x64/include/QtSql", "C:/Qt/install-x64/include/QtTest"], "MOC_INCLUDES_MinSizeRel": ["C:/PROJECT/HighJump/src", "C:/Qt/install-x64/include/QtCore", "C:/Qt/install-x64/include", "C:/Qt/install-x64/mkspecs/win32-arm64-msvc", "C:/Qt/install-x64/include/QtWidgets", "C:/Qt/install-x64/include/QtGui", "C:/Qt/install-x64/include/QtSql", "C:/Qt/install-x64/include/QtTest"], "MOC_INCLUDES_RelWithDebInfo": ["C:/PROJECT/HighJump/src", "C:/Qt/install-x64/include/QtCore", "C:/Qt/install-x64/include", "C:/Qt/install-x64/mkspecs/win32-arm64-msvc", "C:/Qt/install-x64/include/QtWidgets", "C:/Qt/install-x64/include/QtGui", "C:/Qt/install-x64/include/QtSql", "C:/Qt/install-x64/include/QtTest"], "MOC_INCLUDES_Release": ["C:/PROJECT/HighJump/src", "C:/Qt/install-x64/include/QtCore", "C:/Qt/install-x64/include", "C:/Qt/install-x64/mkspecs/win32-arm64-msvc", "C:/Qt/install-x64/include/QtWidgets", "C:/Qt/install-x64/include/QtGui", "C:/Qt/install-x64/include/QtSql", "C:/Qt/install-x64/include/QtTest"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": ["C:/PROJECT/HighJump/build/tests/test_api_client_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_api_client_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_api_client_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_api_client_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_application_startup_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_application_startup_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_application_startup_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_application_startup_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_delegate_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_delegate_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_delegate_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_delegate_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_table_model_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_table_model_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_table_model_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_table_model_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_competition_selection_view_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_competition_selection_view_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_competition_selection_view_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_competition_selection_view_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_config_manager_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_config_manager_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_config_manager_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_config_manager_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_e2e_simple_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_e2e_simple_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_e2e_simple_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_e2e_simple_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_rules_engine_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_rules_engine_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_rules_engine_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_rules_engine_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_scoring_view_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_scoring_view_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_scoring_view_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_scoring_view_autogen/mocs_compilation_Release.cpp"], "MULTI_CONFIG": true, "PARALLEL": 4, "PARSE_CACHE_FILE": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "", "QT_MOC_EXECUTABLE_Debug": "C:/Qt/install-x64/bin/moc.exe", "QT_MOC_EXECUTABLE_MinSizeRel": "C:/Qt/install-x64/bin/moc.exe", "QT_MOC_EXECUTABLE_RelWithDebInfo": "C:/Qt/install-x64/bin/moc.exe", "QT_MOC_EXECUTABLE_Release": "C:/Qt/install-x64/bin/moc.exe", "QT_UIC_EXECUTABLE": "", "QT_UIC_EXECUTABLE_Debug": "C:/Qt/install-x64/bin/uic.exe", "QT_UIC_EXECUTABLE_MinSizeRel": "C:/Qt/install-x64/bin/uic.exe", "QT_UIC_EXECUTABLE_RelWithDebInfo": "C:/Qt/install-x64/bin/uic.exe", "QT_UIC_EXECUTABLE_Release": "C:/Qt/install-x64/bin/uic.exe", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 9, "SETTINGS_FILE": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "C:/PROJECT/HighJump/build/tests/CMakeFiles/test_shortcut_manager_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["C:/PROJECT/HighJump/src/models/athlete.cpp", "MU", null], ["C:/PROJECT/HighJump/src/models/athlete_table_model.cpp", "MU", null], ["C:/PROJECT/HighJump/src/models/competition.cpp", "MU", null], ["C:/PROJECT/HighJump/src/models/jump_attempt.cpp", "MU", null], ["C:/PROJECT/HighJump/src/persistence/database_manager.cpp", "MU", null], ["C:/PROJECT/HighJump/src/ui/shortcut_manager.cpp", "MU", null], ["C:/PROJECT/HighJump/src/utils/config_manager.cpp", "MU", null], ["C:/PROJECT/HighJump/tests/unit/test_shortcut_manager.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": ["C:/PROJECT/HighJump/build/tests/test_api_client_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_api_client_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_api_client_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_api_client_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_application_startup_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_application_startup_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_application_startup_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_application_startup_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_delegate_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_delegate_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_delegate_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_delegate_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_table_model_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_table_model_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_table_model_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_athlete_table_model_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_competition_selection_view_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_competition_selection_view_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_competition_selection_view_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_competition_selection_view_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_config_manager_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_config_manager_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_config_manager_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_config_manager_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_e2e_simple_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_e2e_simple_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_e2e_simple_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_e2e_simple_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_rules_engine_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_rules_engine_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_rules_engine_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_rules_engine_autogen/mocs_compilation_Release.cpp", "C:/PROJECT/HighJump/build/tests/test_scoring_view_autogen/mocs_compilation_Debug.cpp", "C:/PROJECT/HighJump/build/tests/test_scoring_view_autogen/mocs_compilation_MinSizeRel.cpp", "C:/PROJECT/HighJump/build/tests/test_scoring_view_autogen/mocs_compilation_RelWithDebInfo.cpp", "C:/PROJECT/HighJump/build/tests/test_scoring_view_autogen/mocs_compilation_Release.cpp"], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}