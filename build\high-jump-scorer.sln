﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{7F293438-12CA-3838-9090-9A165EB0482E}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{50F1EA66-9AF2-3632-A868-A4181D64B5EB}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
		{EF9487CE-FD72-3F94-8709-3E16A24853C4} = {EF9487CE-FD72-3F94-8709-3E16A24853C4}
		{C0E42CA3-FAEC-3551-9C5D-A06676D437E3} = {C0E42CA3-FAEC-3551-9C5D-A06676D437E3}
		{6F808B5A-D332-358B-BE74-E50E0C4B0762} = {6F808B5A-D332-358B-BE74-E50E0C4B0762}
		{3A15BFF3-9032-374E-8189-951C5C62C80F} = {3A15BFF3-9032-374E-8189-951C5C62C80F}
		{6A0D3B58-2671-35E7-9AB4-3D776542BBFD} = {6A0D3B58-2671-35E7-9AB4-3D776542BBFD}
		{81ED0441-8C96-35E5-A6C1-39A1757715F8} = {81ED0441-8C96-35E5-A6C1-39A1757715F8}
		{8A910539-0962-31B0-A46C-E13D63618B60} = {8A910539-0962-31B0-A46C-E13D63618B60}
		{85326FA8-D048-34D4-835F-F7518A4D63BB} = {85326FA8-D048-34D4-835F-F7518A4D63BB}
		{63EB6FFD-79DC-3660-AC9F-CD7C47E70234} = {63EB6FFD-79DC-3660-AC9F-CD7C47E70234}
		{4A57541E-18E7-3EE2-B242-3157F893C98C} = {4A57541E-18E7-3EE2-B242-3157F893C98C}
		{DFCE6566-AB15-3DE9-9887-DFDE28095787} = {DFCE6566-AB15-3DE9-9887-DFDE28095787}
		{15766974-EA56-3315-91C1-3E5C80A256F7} = {15766974-EA56-3315-91C1-3E5C80A256F7}
		{80DE9FEE-1D03-30AB-BB23-1455165CF44B} = {80DE9FEE-1D03-30AB-BB23-1455165CF44B}
		{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9} = {287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "RUN_TESTS", "RUN_TESTS.vcxproj", "{A8052ADE-7EC6-39BC-B1D6-1A960E8B0E64}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "high-jump-scorer", "high-jump-scorer.vcxproj", "{EF9487CE-FD72-3F94-8709-3E16A24853C4}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_api_client", "tests\test_api_client.vcxproj", "{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_application_startup", "tests\test_application_startup.vcxproj", "{6F808B5A-D332-358B-BE74-E50E0C4B0762}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_athlete_delegate", "tests\test_athlete_delegate.vcxproj", "{3A15BFF3-9032-374E-8189-951C5C62C80F}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_athlete_table_model", "tests\test_athlete_table_model.vcxproj", "{6A0D3B58-2671-35E7-9AB4-3D776542BBFD}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_competition_selection_view", "tests\test_competition_selection_view.vcxproj", "{81ED0441-8C96-35E5-A6C1-39A1757715F8}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_config_manager", "tests\test_config_manager.vcxproj", "{8A910539-0962-31B0-A46C-E13D63618B60}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_database_manager", "tests\test_database_manager.vcxproj", "{85326FA8-D048-34D4-835F-F7518A4D63BB}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_e2e_simple", "tests\test_e2e_simple.vcxproj", "{63EB6FFD-79DC-3660-AC9F-CD7C47E70234}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_large_scale_performance", "tests\test_large_scale_performance.vcxproj", "{4A57541E-18E7-3EE2-B242-3157F893C98C}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_rules_engine", "tests\test_rules_engine.vcxproj", "{DFCE6566-AB15-3DE9-9887-DFDE28095787}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_scoring_view", "tests\test_scoring_view.vcxproj", "{15766974-EA56-3315-91C1-3E5C80A256F7}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_shortcut_manager", "tests\test_shortcut_manager.vcxproj", "{80DE9FEE-1D03-30AB-BB23-1455165CF44B}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "test_ui_ux_usability", "tests\test_ui_ux_usability.vcxproj", "{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}"
	ProjectSection(ProjectDependencies) = postProject
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {F9C87542-EA78-3B7C-B54D-2CB91B9FD646}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Release|x64 = Release|x64
		MinSizeRel|x64 = MinSizeRel|x64
		RelWithDebInfo|x64 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{50F1EA66-9AF2-3632-A868-A4181D64B5EB}.Debug|x64.ActiveCfg = Debug|x64
		{50F1EA66-9AF2-3632-A868-A4181D64B5EB}.Release|x64.ActiveCfg = Release|x64
		{50F1EA66-9AF2-3632-A868-A4181D64B5EB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{50F1EA66-9AF2-3632-A868-A4181D64B5EB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{A8052ADE-7EC6-39BC-B1D6-1A960E8B0E64}.Debug|x64.ActiveCfg = Debug|x64
		{A8052ADE-7EC6-39BC-B1D6-1A960E8B0E64}.Release|x64.ActiveCfg = Release|x64
		{A8052ADE-7EC6-39BC-B1D6-1A960E8B0E64}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{A8052ADE-7EC6-39BC-B1D6-1A960E8B0E64}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}.Debug|x64.ActiveCfg = Debug|x64
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}.Debug|x64.Build.0 = Debug|x64
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}.Release|x64.ActiveCfg = Release|x64
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}.Release|x64.Build.0 = Release|x64
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{EF9487CE-FD72-3F94-8709-3E16A24853C4}.Debug|x64.ActiveCfg = Debug|x64
		{EF9487CE-FD72-3F94-8709-3E16A24853C4}.Debug|x64.Build.0 = Debug|x64
		{EF9487CE-FD72-3F94-8709-3E16A24853C4}.Release|x64.ActiveCfg = Release|x64
		{EF9487CE-FD72-3F94-8709-3E16A24853C4}.Release|x64.Build.0 = Release|x64
		{EF9487CE-FD72-3F94-8709-3E16A24853C4}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{EF9487CE-FD72-3F94-8709-3E16A24853C4}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{EF9487CE-FD72-3F94-8709-3E16A24853C4}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{EF9487CE-FD72-3F94-8709-3E16A24853C4}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}.Debug|x64.ActiveCfg = Debug|x64
		{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}.Debug|x64.Build.0 = Debug|x64
		{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}.Release|x64.ActiveCfg = Release|x64
		{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}.Release|x64.Build.0 = Release|x64
		{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{C0E42CA3-FAEC-3551-9C5D-A06676D437E3}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{6F808B5A-D332-358B-BE74-E50E0C4B0762}.Debug|x64.ActiveCfg = Debug|x64
		{6F808B5A-D332-358B-BE74-E50E0C4B0762}.Debug|x64.Build.0 = Debug|x64
		{6F808B5A-D332-358B-BE74-E50E0C4B0762}.Release|x64.ActiveCfg = Release|x64
		{6F808B5A-D332-358B-BE74-E50E0C4B0762}.Release|x64.Build.0 = Release|x64
		{6F808B5A-D332-358B-BE74-E50E0C4B0762}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6F808B5A-D332-358B-BE74-E50E0C4B0762}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6F808B5A-D332-358B-BE74-E50E0C4B0762}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6F808B5A-D332-358B-BE74-E50E0C4B0762}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{3A15BFF3-9032-374E-8189-951C5C62C80F}.Debug|x64.ActiveCfg = Debug|x64
		{3A15BFF3-9032-374E-8189-951C5C62C80F}.Debug|x64.Build.0 = Debug|x64
		{3A15BFF3-9032-374E-8189-951C5C62C80F}.Release|x64.ActiveCfg = Release|x64
		{3A15BFF3-9032-374E-8189-951C5C62C80F}.Release|x64.Build.0 = Release|x64
		{3A15BFF3-9032-374E-8189-951C5C62C80F}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{3A15BFF3-9032-374E-8189-951C5C62C80F}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{3A15BFF3-9032-374E-8189-951C5C62C80F}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{3A15BFF3-9032-374E-8189-951C5C62C80F}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{6A0D3B58-2671-35E7-9AB4-3D776542BBFD}.Debug|x64.ActiveCfg = Debug|x64
		{6A0D3B58-2671-35E7-9AB4-3D776542BBFD}.Debug|x64.Build.0 = Debug|x64
		{6A0D3B58-2671-35E7-9AB4-3D776542BBFD}.Release|x64.ActiveCfg = Release|x64
		{6A0D3B58-2671-35E7-9AB4-3D776542BBFD}.Release|x64.Build.0 = Release|x64
		{6A0D3B58-2671-35E7-9AB4-3D776542BBFD}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{6A0D3B58-2671-35E7-9AB4-3D776542BBFD}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{6A0D3B58-2671-35E7-9AB4-3D776542BBFD}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{6A0D3B58-2671-35E7-9AB4-3D776542BBFD}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{81ED0441-8C96-35E5-A6C1-39A1757715F8}.Debug|x64.ActiveCfg = Debug|x64
		{81ED0441-8C96-35E5-A6C1-39A1757715F8}.Debug|x64.Build.0 = Debug|x64
		{81ED0441-8C96-35E5-A6C1-39A1757715F8}.Release|x64.ActiveCfg = Release|x64
		{81ED0441-8C96-35E5-A6C1-39A1757715F8}.Release|x64.Build.0 = Release|x64
		{81ED0441-8C96-35E5-A6C1-39A1757715F8}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{81ED0441-8C96-35E5-A6C1-39A1757715F8}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{81ED0441-8C96-35E5-A6C1-39A1757715F8}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{81ED0441-8C96-35E5-A6C1-39A1757715F8}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{8A910539-0962-31B0-A46C-E13D63618B60}.Debug|x64.ActiveCfg = Debug|x64
		{8A910539-0962-31B0-A46C-E13D63618B60}.Debug|x64.Build.0 = Debug|x64
		{8A910539-0962-31B0-A46C-E13D63618B60}.Release|x64.ActiveCfg = Release|x64
		{8A910539-0962-31B0-A46C-E13D63618B60}.Release|x64.Build.0 = Release|x64
		{8A910539-0962-31B0-A46C-E13D63618B60}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{8A910539-0962-31B0-A46C-E13D63618B60}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{8A910539-0962-31B0-A46C-E13D63618B60}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{8A910539-0962-31B0-A46C-E13D63618B60}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{85326FA8-D048-34D4-835F-F7518A4D63BB}.Debug|x64.ActiveCfg = Debug|x64
		{85326FA8-D048-34D4-835F-F7518A4D63BB}.Debug|x64.Build.0 = Debug|x64
		{85326FA8-D048-34D4-835F-F7518A4D63BB}.Release|x64.ActiveCfg = Release|x64
		{85326FA8-D048-34D4-835F-F7518A4D63BB}.Release|x64.Build.0 = Release|x64
		{85326FA8-D048-34D4-835F-F7518A4D63BB}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{85326FA8-D048-34D4-835F-F7518A4D63BB}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{85326FA8-D048-34D4-835F-F7518A4D63BB}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{85326FA8-D048-34D4-835F-F7518A4D63BB}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{63EB6FFD-79DC-3660-AC9F-CD7C47E70234}.Debug|x64.ActiveCfg = Debug|x64
		{63EB6FFD-79DC-3660-AC9F-CD7C47E70234}.Debug|x64.Build.0 = Debug|x64
		{63EB6FFD-79DC-3660-AC9F-CD7C47E70234}.Release|x64.ActiveCfg = Release|x64
		{63EB6FFD-79DC-3660-AC9F-CD7C47E70234}.Release|x64.Build.0 = Release|x64
		{63EB6FFD-79DC-3660-AC9F-CD7C47E70234}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{63EB6FFD-79DC-3660-AC9F-CD7C47E70234}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{63EB6FFD-79DC-3660-AC9F-CD7C47E70234}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{63EB6FFD-79DC-3660-AC9F-CD7C47E70234}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{4A57541E-18E7-3EE2-B242-3157F893C98C}.Debug|x64.ActiveCfg = Debug|x64
		{4A57541E-18E7-3EE2-B242-3157F893C98C}.Debug|x64.Build.0 = Debug|x64
		{4A57541E-18E7-3EE2-B242-3157F893C98C}.Release|x64.ActiveCfg = Release|x64
		{4A57541E-18E7-3EE2-B242-3157F893C98C}.Release|x64.Build.0 = Release|x64
		{4A57541E-18E7-3EE2-B242-3157F893C98C}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{4A57541E-18E7-3EE2-B242-3157F893C98C}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{4A57541E-18E7-3EE2-B242-3157F893C98C}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{4A57541E-18E7-3EE2-B242-3157F893C98C}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{DFCE6566-AB15-3DE9-9887-DFDE28095787}.Debug|x64.ActiveCfg = Debug|x64
		{DFCE6566-AB15-3DE9-9887-DFDE28095787}.Debug|x64.Build.0 = Debug|x64
		{DFCE6566-AB15-3DE9-9887-DFDE28095787}.Release|x64.ActiveCfg = Release|x64
		{DFCE6566-AB15-3DE9-9887-DFDE28095787}.Release|x64.Build.0 = Release|x64
		{DFCE6566-AB15-3DE9-9887-DFDE28095787}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{DFCE6566-AB15-3DE9-9887-DFDE28095787}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{DFCE6566-AB15-3DE9-9887-DFDE28095787}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{DFCE6566-AB15-3DE9-9887-DFDE28095787}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{15766974-EA56-3315-91C1-3E5C80A256F7}.Debug|x64.ActiveCfg = Debug|x64
		{15766974-EA56-3315-91C1-3E5C80A256F7}.Debug|x64.Build.0 = Debug|x64
		{15766974-EA56-3315-91C1-3E5C80A256F7}.Release|x64.ActiveCfg = Release|x64
		{15766974-EA56-3315-91C1-3E5C80A256F7}.Release|x64.Build.0 = Release|x64
		{15766974-EA56-3315-91C1-3E5C80A256F7}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{15766974-EA56-3315-91C1-3E5C80A256F7}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{15766974-EA56-3315-91C1-3E5C80A256F7}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{15766974-EA56-3315-91C1-3E5C80A256F7}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{80DE9FEE-1D03-30AB-BB23-1455165CF44B}.Debug|x64.ActiveCfg = Debug|x64
		{80DE9FEE-1D03-30AB-BB23-1455165CF44B}.Debug|x64.Build.0 = Debug|x64
		{80DE9FEE-1D03-30AB-BB23-1455165CF44B}.Release|x64.ActiveCfg = Release|x64
		{80DE9FEE-1D03-30AB-BB23-1455165CF44B}.Release|x64.Build.0 = Release|x64
		{80DE9FEE-1D03-30AB-BB23-1455165CF44B}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{80DE9FEE-1D03-30AB-BB23-1455165CF44B}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{80DE9FEE-1D03-30AB-BB23-1455165CF44B}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{80DE9FEE-1D03-30AB-BB23-1455165CF44B}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
		{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}.Debug|x64.ActiveCfg = Debug|x64
		{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}.Debug|x64.Build.0 = Debug|x64
		{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}.Release|x64.ActiveCfg = Release|x64
		{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}.Release|x64.Build.0 = Release|x64
		{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}.MinSizeRel|x64.ActiveCfg = MinSizeRel|x64
		{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}.MinSizeRel|x64.Build.0 = MinSizeRel|x64
		{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}.RelWithDebInfo|x64.ActiveCfg = RelWithDebInfo|x64
		{287BAE6A-090D-3448-AF5B-EA7FDFE1A2B9}.RelWithDebInfo|x64.Build.0 = RelWithDebInfo|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{50F1EA66-9AF2-3632-A868-A4181D64B5EB} = {7F293438-12CA-3838-9090-9A165EB0482E}
		{A8052ADE-7EC6-39BC-B1D6-1A960E8B0E64} = {7F293438-12CA-3838-9090-9A165EB0482E}
		{F9C87542-EA78-3B7C-B54D-2CB91B9FD646} = {7F293438-12CA-3838-9090-9A165EB0482E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FDDC1A85-AC36-31FA-88BC-7A9CE029453C}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
