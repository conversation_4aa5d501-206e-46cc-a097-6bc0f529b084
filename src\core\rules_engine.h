#ifndef RULES_ENGINE_H
#define RULES_ENGINE_H

#include <QObject>
#include <QList>
#include <QMutex>
#include "models/ranking_calculator.h"

class Competition;
class DatabaseManager;

/**
 * @brief 跳高比赛规则引擎
 * 
 * 这个类作为跳高比赛规则的核心引擎，负责：
 * - 排名计算和更新
 * - 比赛规则验证
 * - 状态管理协调
 * - 实时排名更新
 * 
 * 设计为单例模式，确保全局一致的规则执行
 */
class RulesEngine : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 排名条目数据结构
     */
    struct RankingEntry {
        int athleteId;              // 运动员ID
        int rank;                   // 排名（1-based）
        int bestHeight;             // 最佳成绩（厘米）
        int totalFailures;          // 总失败次数
        int failuresAtBest;         // 最佳高度失败次数
        
        // 比较运算符用于排序
        bool operator<(const RankingEntry &other) const;
    };

    /**
     * @brief 获取单例实例
     * @return RulesEngine实例指针
     */
    static RulesEngine* instance();

    /**
     * @brief 销毁单例实例
     */
    static void destroyInstance();

    /**
     * @brief 计算当前排名
     * @param competitionId 比赛ID
     * @return 排名列表
     */
    QList<RankingEntry> calculateRanking(int competitionId);

    /**
     * @brief 设置数据库管理器
     * @param dbManager 数据库管理器指针
     */
    void setDatabaseManager(DatabaseManager *dbManager);

    /**
     * @brief 设置排名计算器
     * @param calculator 排名计算器指针
     */
    void setRankingCalculator(RankingCalculator *calculator);

signals:
    /**
     * @brief 排名更新信号
     * @param rankings 新的排名列表
     */
    void rankingUpdated(const QList<RankingEntry> &rankings);

    /**
     * @brief 排名计算错误信号
     * @param error 错误信息
     */
    void rankingError(const QString &error);

private:
    explicit RulesEngine(QObject *parent = nullptr);
    ~RulesEngine();

    // 禁用拷贝构造和赋值
    RulesEngine(const RulesEngine&) = delete;
    RulesEngine& operator=(const RulesEngine&) = delete;

    // 内部方法
    QList<RankingEntry> convertFromCalculatorResult(const RankingCalculator::RankingResult &result);
    bool validateRankingData(const QList<RankingEntry> &rankings);

    // 成员变量
    DatabaseManager *m_dbManager;
    RankingCalculator *m_rankingCalculator;
    
    // 线程安全
    mutable QMutex m_mutex;
    
    // 单例实例
    static RulesEngine *s_instance;
    static QMutex s_instanceMutex;
};

// 使排名条目可以在Qt信号槽中使用
Q_DECLARE_METATYPE(RulesEngine::RankingEntry)

#endif // RULES_ENGINE_H
