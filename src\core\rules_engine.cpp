#include "rules_engine.h"
#include "persistence/database_manager.h"
#include "models/competition.h"
#include "models/athlete.h"
#include <QDebug>
#include <QMutexLocker>
#include <QSqlQuery>
#include <QSqlError>
#include <algorithm>

// 静态成员初始化
RulesEngine* RulesEngine::s_instance = nullptr;
QMutex RulesEngine::s_instanceMutex;

RulesEngine::RulesEngine(QObject *parent)
    : QObject(parent)
    , m_dbManager(nullptr)
    , m_rankingCalculator(nullptr)
{
    qDebug() << "RulesEngine: Initialized";
}

RulesEngine::~RulesEngine()
{
    qDebug() << "RulesEngine: Destroyed";
}

RulesEngine* RulesEngine::instance()
{
    QMutexLocker locker(&s_instanceMutex);
    if (!s_instance) {
        s_instance = new RulesEngine();
    }
    return s_instance;
}

void RulesEngine::destroyInstance()
{
    QMutexLocker locker(&s_instanceMutex);
    delete s_instance;
    s_instance = nullptr;
}

bool RulesEngine::RankingEntry::operator<(const RankingEntry &other) const
{
    // 官方跳高排名规则：
    // 1. 最佳成绩（越高越好）
    if (bestHeight != other.bestHeight) {
        return bestHeight > other.bestHeight;
    }
    
    // 2. 最佳高度失败次数（越少越好）
    if (failuresAtBest != other.failuresAtBest) {
        return failuresAtBest < other.failuresAtBest;
    }
    
    // 3. 总失败次数（越少越好）
    if (totalFailures != other.totalFailures) {
        return totalFailures < other.totalFailures;
    }
    
    // 4. 运动员ID（保持稳定排序）
    return athleteId < other.athleteId;
}

QList<RulesEngine::RankingEntry> RulesEngine::calculateRanking(int competitionId)
{
    QMutexLocker locker(&m_mutex);
    
    QList<RankingEntry> rankings;
    
    if (!m_dbManager) {
        qWarning() << "RulesEngine::calculateRanking: Database manager not set";
        emit rankingError(tr("Database manager not available"));
        return rankings;
    }
    
    try {
        // 使用数据库视图查询排名数据
        QSqlQuery query;
        query.prepare(R"(
            SELECT 
                id as athlete_id,
                best_height,
                failures_at_best,
                total_failures,
                ROW_NUMBER() OVER (
                    ORDER BY 
                        best_height DESC,
                        failures_at_best ASC,
                        total_failures ASC,
                        number ASC
                ) as rank
            FROM athlete_summary 
            WHERE competition_id = ? 
            AND status IN ('active', 'finished')
            ORDER BY rank
        )");
        
        query.addBindValue(competitionId);
        
        if (!query.exec()) {
            QString error = tr("Failed to calculate rankings: %1").arg(query.lastError().text());
            qWarning() << "RulesEngine::calculateRanking:" << error;
            emit rankingError(error);
            return rankings;
        }
        
        // 处理查询结果
        while (query.next()) {
            RankingEntry entry;
            entry.athleteId = query.value("athlete_id").toInt();
            entry.rank = query.value("rank").toInt();
            entry.bestHeight = query.value("best_height").toInt();
            entry.failuresAtBest = query.value("failures_at_best").toInt();
            entry.totalFailures = query.value("total_failures").toInt();
            
            rankings.append(entry);
        }
        
        // 验证排名数据
        if (!validateRankingData(rankings)) {
            qWarning() << "RulesEngine::calculateRanking: Invalid ranking data generated";
            emit rankingError(tr("Invalid ranking data"));
            return QList<RankingEntry>();
        }
        
        qDebug() << "RulesEngine::calculateRanking: Calculated rankings for" << rankings.size() << "athletes";
        
        // 发射排名更新信号
        emit rankingUpdated(rankings);
        
    } catch (const std::exception &e) {
        QString error = tr("Exception during ranking calculation: %1").arg(e.what());
        qCritical() << "RulesEngine::calculateRanking:" << error;
        emit rankingError(error);
        return QList<RankingEntry>();
    }
    
    return rankings;
}

void RulesEngine::setDatabaseManager(DatabaseManager *dbManager)
{
    QMutexLocker locker(&m_mutex);
    m_dbManager = dbManager;
    qDebug() << "RulesEngine::setDatabaseManager: Database manager set";
}

void RulesEngine::setRankingCalculator(RankingCalculator *calculator)
{
    QMutexLocker locker(&m_mutex);
    m_rankingCalculator = calculator;
    qDebug() << "RulesEngine::setRankingCalculator: Ranking calculator set";
}

QList<RulesEngine::RankingEntry> RulesEngine::convertFromCalculatorResult(const RankingCalculator::RankingResult &result)
{
    QList<RankingEntry> rankings;
    
    for (const auto &athleteRanking : result.rankings) {
        RankingEntry entry;
        entry.athleteId = athleteRanking.athleteId;
        entry.rank = athleteRanking.rank;
        entry.bestHeight = athleteRanking.bestHeight;
        entry.totalFailures = athleteRanking.totalFailures;
        entry.failuresAtBest = athleteRanking.failuresAtBest;
        
        rankings.append(entry);
    }
    
    return rankings;
}

bool RulesEngine::validateRankingData(const QList<RankingEntry> &rankings)
{
    if (rankings.isEmpty()) {
        return true; // 空排名是有效的
    }
    
    // 检查排名连续性和唯一性
    QSet<int> athleteIds;
    QSet<int> ranks;
    
    for (const auto &entry : rankings) {
        // 检查运动员ID唯一性
        if (athleteIds.contains(entry.athleteId)) {
            qWarning() << "RulesEngine::validateRankingData: Duplicate athlete ID:" << entry.athleteId;
            return false;
        }
        athleteIds.insert(entry.athleteId);
        
        // 检查排名有效性
        if (entry.rank < 1 || entry.rank > rankings.size()) {
            qWarning() << "RulesEngine::validateRankingData: Invalid rank:" << entry.rank;
            return false;
        }
        
        // 检查数据有效性
        if (entry.bestHeight < 0 || entry.totalFailures < 0 || entry.failuresAtBest < 0) {
            qWarning() << "RulesEngine::validateRankingData: Invalid data for athlete:" << entry.athleteId;
            return false;
        }
    }
    
    return true;
}
