{"artifacts": [{"path": "bin/RelWithDebInfo/test_database_manager.exe"}, {"path": "bin/RelWithDebInfo/test_database_manager.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "include_directories"], "files": ["C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "tests/CMakeLists.txt", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake", "CMakeLists.txt"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 8, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 47, "parent": 0}, {"command": 4, "file": 0, "line": 640, "parent": 2}, {"file": 5}, {"command": 7, "file": 5, "line": 14, "parent": 7}, {"file": 4, "parent": 8}, {"command": 7, "file": 4, "line": 218, "parent": 9}, {"file": 3, "parent": 10}, {"command": 6, "file": 3, "line": 57, "parent": 11}, {"file": 2, "parent": 12}, {"command": 5, "file": 2, "line": 61, "parent": 13}, {"command": 8, "file": 5, "line": 27, "parent": 7}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /Zi /O2 /Ob1 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 6, "fragment": "-Zc:__cplusplus"}, {"backtrace": 6, "fragment": "-permissive-"}, {"backtrace": 6, "fragment": "-utf-8"}], "defines": [{"backtrace": 6, "define": "QT_CORE_LIB"}, {"backtrace": 6, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\""}, {"backtrace": 5, "define": "QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\""}, {"backtrace": 5, "define": "QT_TESTLIB_LIB"}, {"backtrace": 6, "define": "UNICODE"}, {"backtrace": 6, "define": "WIN32"}, {"backtrace": 6, "define": "WIN64"}, {"backtrace": 6, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 6, "define": "_UNICODE"}, {"backtrace": 6, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/PROJECT/HighJump/build/tests/test_database_manager_autogen/include_RelWithDebInfo"}, {"backtrace": 15, "path": "C:/PROJECT/HighJump/src"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/install-x64/include/QtCore"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/install-x64/include"}, {"backtrace": 6, "isSystem": true, "path": "C:/Qt/install-x64/mkspecs/win32-arm64-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtTest"}], "language": "CXX", "languageStandard": {"backtraces": [6, 6], "standard": "17"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "test_database_manager::@a44f0ac069e85531cdee", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /Zi /O2 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /debug /INCREMENTAL /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Sql.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Test.lib", "role": "libraries"}, {"backtrace": 6, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Core.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 14, "fragment": "userenv.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "test_database_manager", "nameOnDisk": "test_database_manager.exe", "paths": {"build": "tests", "source": "tests"}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/tests/test_database_manager_autogen/mocs_compilation_RelWithDebInfo.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "tests/unit/test_database_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/persistence/database_manager.cpp", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}