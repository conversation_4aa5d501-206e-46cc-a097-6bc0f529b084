{"artifacts": [{"path": "bin/Release/test_config_manager.exe"}, {"path": "bin/Release/test_config_manager.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "include_directories"], "files": ["C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "tests/CMakeLists.txt", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake", "CMakeLists.txt", "C:/Qt/install-x64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 13, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 53, "parent": 0}, {"file": 5}, {"command": 7, "file": 5, "line": 14, "parent": 6}, {"file": 4, "parent": 7}, {"command": 7, "file": 4, "line": 218, "parent": 8}, {"file": 3, "parent": 9}, {"command": 6, "file": 3, "line": 55, "parent": 10}, {"file": 2, "parent": 11}, {"command": 5, "file": 2, "line": 61, "parent": 12}, {"command": 6, "file": 3, "line": 43, "parent": 10}, {"file": 10, "parent": 14}, {"command": 9, "file": 10, "line": 45, "parent": 15}, {"command": 8, "file": 9, "line": 137, "parent": 16}, {"command": 7, "file": 8, "line": 76, "parent": 17}, {"file": 7, "parent": 18}, {"command": 6, "file": 7, "line": 55, "parent": 19}, {"file": 6, "parent": 20}, {"command": 5, "file": 6, "line": 61, "parent": 21}, {"command": 4, "file": 0, "line": 640, "parent": 2}, {"command": 7, "file": 4, "line": 218, "parent": 8}, {"file": 12, "parent": 24}, {"command": 6, "file": 12, "line": 57, "parent": 25}, {"file": 11, "parent": 26}, {"command": 5, "file": 11, "line": 61, "parent": 27}, {"command": 10, "file": 5, "line": 27, "parent": 6}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 23, "fragment": "-Zc:__cplusplus"}, {"backtrace": 23, "fragment": "-permissive-"}, {"backtrace": 23, "fragment": "-utf-8"}], "defines": [{"backtrace": 23, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 23, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_TESTCASE_BUILDDIR=\"C:/PROJECT/HighJump/build/tests\""}, {"backtrace": 5, "define": "QT_TESTCASE_SOURCEDIR=\"C:/PROJECT/HighJump/tests\""}, {"backtrace": 5, "define": "QT_TESTLIB_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 23, "define": "UNICODE"}, {"backtrace": 23, "define": "WIN32"}, {"backtrace": 23, "define": "WIN64"}, {"backtrace": 23, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 23, "define": "_UNICODE"}, {"backtrace": 23, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/PROJECT/HighJump/build/tests/test_config_manager_autogen/include_Release"}, {"backtrace": 29, "path": "C:/PROJECT/HighJump/src"}, {"backtrace": 23, "isSystem": true, "path": "C:/Qt/install-x64/include/QtCore"}, {"backtrace": 23, "isSystem": true, "path": "C:/Qt/install-x64/include"}, {"backtrace": 23, "isSystem": true, "path": "C:/Qt/install-x64/mkspecs/win32-arm64-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtTest"}], "language": "CXX", "languageStandard": {"backtraces": [23, 23], "standard": "17"}, "sourceIndexes": [0, 1, 2]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "test_config_manager::@a44f0ac069e85531cdee", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:console", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Widgets.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Test.lib", "role": "libraries"}, {"backtrace": 13, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Gui.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 22, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Core.lib", "role": "libraries"}, {"backtrace": 28, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 28, "fragment": "userenv.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "test_config_manager", "nameOnDisk": "test_config_manager.exe", "paths": {"build": "tests", "source": "tests"}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1, 2]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/tests/test_config_manager_autogen/mocs_compilation_Release.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "tests/unit/test_config_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/utils/config_manager.cpp", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}