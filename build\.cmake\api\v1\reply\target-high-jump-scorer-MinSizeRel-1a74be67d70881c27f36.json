{"artifacts": [{"path": "bin/MinSizeRel/high-jump-scorer.exe"}, {"path": "bin/MinSizeRel/high-jump-scorer.pdb"}], "backtrace": 4, "backtraceGraph": {"commands": ["add_executable", "_qt_internal_create_executable", "qt6_add_executable", "qt_add_executable", "target_link_libraries", "set_target_properties", "include", "find_package", "find_dependency", "_qt_internal_find_qt_dependencies", "include_directories"], "files": ["C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "CMakeLists.txt", "C:/Qt/install-x64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Program Files/Microsoft Visual Studio/2022/Community/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/install-x64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Qt/install-x64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt/install-x64/lib/cmake/Qt6PrintSupport/Qt6PrintSupportTargets.cmake", "C:/Qt/install-x64/lib/cmake/Qt6PrintSupport/Qt6PrintSupportConfig.cmake"], "nodes": [{"file": 1}, {"command": 3, "file": 1, "line": 48, "parent": 0}, {"command": 2, "file": 0, "line": 935, "parent": 1}, {"command": 1, "file": 0, "line": 639, "parent": 2}, {"command": 0, "file": 0, "line": 690, "parent": 3}, {"command": 4, "file": 1, "line": 53, "parent": 0}, {"command": 7, "file": 1, "line": 14, "parent": 0}, {"file": 4, "parent": 6}, {"command": 7, "file": 4, "line": 218, "parent": 7}, {"file": 3, "parent": 8}, {"command": 6, "file": 3, "line": 55, "parent": 9}, {"file": 2, "parent": 10}, {"command": 5, "file": 2, "line": 61, "parent": 11}, {"command": 7, "file": 4, "line": 218, "parent": 7}, {"file": 6, "parent": 13}, {"command": 6, "file": 6, "line": 55, "parent": 14}, {"file": 5, "parent": 15}, {"command": 5, "file": 5, "line": 61, "parent": 16}, {"command": 4, "file": 0, "line": 640, "parent": 2}, {"command": 7, "file": 4, "line": 218, "parent": 7}, {"file": 8, "parent": 19}, {"command": 6, "file": 8, "line": 57, "parent": 20}, {"file": 7, "parent": 21}, {"command": 5, "file": 7, "line": 61, "parent": 22}, {"command": 6, "file": 8, "line": 45, "parent": 20}, {"file": 13, "parent": 24}, {"command": 9, "file": 13, "line": 46, "parent": 25}, {"command": 8, "file": 12, "line": 137, "parent": 26}, {"command": 7, "file": 11, "line": 76, "parent": 27}, {"file": 10, "parent": 28}, {"command": 6, "file": 10, "line": 55, "parent": 29}, {"file": 9, "parent": 30}, {"command": 5, "file": 9, "line": 61, "parent": 31}, {"command": 5, "file": 9, "line": 83, "parent": 31}, {"command": 6, "file": 6, "line": 43, "parent": 14}, {"file": 16, "parent": 34}, {"command": 9, "file": 16, "line": 45, "parent": 35}, {"command": 8, "file": 12, "line": 137, "parent": 36}, {"command": 7, "file": 11, "line": 76, "parent": 37}, {"file": 15, "parent": 38}, {"command": 6, "file": 15, "line": 55, "parent": 39}, {"file": 14, "parent": 40}, {"command": 5, "file": 14, "line": 61, "parent": 41}, {"command": 7, "file": 4, "line": 218, "parent": 7}, {"file": 18, "parent": 43}, {"command": 6, "file": 18, "line": 55, "parent": 44}, {"file": 17, "parent": 45}, {"command": 5, "file": 17, "line": 61, "parent": 46}, {"command": 10, "file": 1, "line": 27, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O1 /Ob1 /DNDEBUG -std:c++17 -MD"}, {"backtrace": 18, "fragment": "-Zc:__cplusplus"}, {"backtrace": 18, "fragment": "-permissive-"}, {"backtrace": 18, "fragment": "-utf-8"}], "defines": [{"backtrace": 18, "define": "QT_CORE_LIB"}, {"backtrace": 5, "define": "QT_GUI_LIB"}, {"backtrace": 5, "define": "QT_NETWORK_LIB"}, {"backtrace": 18, "define": "QT_NO_DEBUG"}, {"backtrace": 5, "define": "QT_PRINTSUPPORT_LIB"}, {"backtrace": 5, "define": "QT_SQL_LIB"}, {"backtrace": 5, "define": "QT_WIDGETS_LIB"}, {"backtrace": 18, "define": "UNICODE"}, {"backtrace": 18, "define": "WIN32"}, {"backtrace": 18, "define": "WIN64"}, {"backtrace": 18, "define": "_ENABLE_EXTENDED_ALIGNED_STORAGE"}, {"backtrace": 18, "define": "_UNICODE"}, {"backtrace": 18, "define": "_WIN64"}], "includes": [{"backtrace": 0, "path": "C:/PROJECT/HighJump/build/high-jump-scorer_autogen/include_MinSizeRel"}, {"backtrace": 48, "path": "C:/PROJECT/HighJump/src"}, {"backtrace": 18, "isSystem": true, "path": "C:/Qt/install-x64/include/QtCore"}, {"backtrace": 18, "isSystem": true, "path": "C:/Qt/install-x64/include"}, {"backtrace": 18, "isSystem": true, "path": "C:/Qt/install-x64/mkspecs/win32-arm64-msvc"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtWidgets"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtGui"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtSql"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtNetwork"}, {"backtrace": 5, "isSystem": true, "path": "C:/Qt/install-x64/include/QtPrintSupport"}], "language": "CXX", "languageStandard": {"backtraces": [18, 18], "standard": "17"}, "sourceIndexes": [0, 1, 3, 5, 7, 9, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "id": "high-jump-scorer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O1 /Ob1 /DNDEBUG -MD", "role": "flags"}, {"fragment": "/machine:x64 /INCREMENTAL:NO /subsystem:windows", "role": "flags"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Sql.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Network.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6PrintSupport.lib", "role": "libraries"}, {"backtrace": 12, "fragment": "ws2_32.lib", "role": "libraries"}, {"backtrace": 5, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Widgets.lib", "role": "libraries"}, {"backtrace": 17, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Gui.lib", "role": "libraries"}, {"backtrace": 18, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6Core.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "mpr.lib", "role": "libraries"}, {"backtrace": 23, "fragment": "userenv.lib", "role": "libraries"}, {"backtrace": 32, "fragment": "C:\\Qt\\install-x64\\lib\\Qt6EntryPoint.lib", "role": "libraries"}, {"backtrace": 33, "fragment": "shell32.lib", "role": "libraries"}, {"backtrace": 42, "fragment": "d3d11.lib", "role": "libraries"}, {"backtrace": 42, "fragment": "dxgi.lib", "role": "libraries"}, {"backtrace": 42, "fragment": "dxguid.lib", "role": "libraries"}, {"backtrace": 42, "fragment": "d3d12.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "comdlg32.lib", "role": "libraries"}, {"backtrace": 47, "fragment": "winspool.lib", "role": "libraries"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "high-jump-scorer", "nameOnDisk": "high-jump-scorer.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files\\Generated", "sourceIndexes": [0]}, {"name": "Source Files", "sourceIndexes": [1, 3, 5, 7, 9, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36, 38, 40, 42, 44, 46, 48, 50]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [2, 4, 6, 8, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51]}], "sources": [{"backtrace": 0, "compileGroupIndex": 0, "isGenerated": true, "path": "build/high-jump-scorer_autogen/mocs_compilation_MinSizeRel.cpp", "sourceGroupIndex": 0}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/api/api_client.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/api/api_client.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/api/competition_dto.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/api/competition_dto.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/core/jump_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/core/jump_manager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/core/rules_engine.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/core/rules_engine.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/main.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/models/athlete.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/models/athlete.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/models/athlete_table_model.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/models/athlete_table_model.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/models/competition.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/models/competition.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/models/competition_state.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/models/competition_state.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/models/jump_attempt.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/models/jump_attempt.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/models/ranking_calculator.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/models/ranking_calculator.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/persistence/database_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/persistence/database_manager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/persistence/sync_queue_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/persistence/sync_queue_manager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/athlete_delegate.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/athlete_delegate.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/athlete_dialog.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/athlete_dialog.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/competition_selection_view.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/competition_selection_view.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/main_window.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/main_window.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/ranking_panel.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/ranking_panel.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/report_dialog.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/report_dialog.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/scoring_view.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/scoring_view.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/shortcut_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/shortcut_manager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/theme_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/theme_manager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/ui/view_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/ui/view_manager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/utils/config_manager.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/utils/config_manager.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/utils/performance_monitor.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/utils/performance_monitor.h", "sourceGroupIndex": 2}, {"backtrace": 4, "compileGroupIndex": 0, "path": "src/utils/report_generator.cpp", "sourceGroupIndex": 1}, {"backtrace": 4, "path": "src/utils/report_generator.h", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}